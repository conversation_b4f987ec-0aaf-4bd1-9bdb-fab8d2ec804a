<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员登录 - Music Player Admin</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            color: #ffffff;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .login-container {
            max-width: 450px;
            width: 100%;
            margin: 2rem;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 25px;
            padding: 3rem;
            box-shadow: 0 30px 60px rgba(0, 0, 0, 0.4);
            animation: fadeInUp 0.8s ease;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .login-header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .login-header h4 {
            font-weight: 300;
            font-size: 2rem;
            margin-bottom: 0.5rem;
            background: linear-gradient(45deg, #ffffff, #cccccc);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .login-header p {
            color: #888888;
            font-size: 1rem;
        }

        .login-form .form-group {
            margin-bottom: 2rem;
            position: relative;
        }

        .login-form .form-label {
            display: block;
            margin-bottom: 0.8rem;
            color: #cccccc;
            font-weight: 500;
            font-size: 0.9rem;
        }

        .login-form .form-control {
            width: 100%;
            padding: 1rem 1rem 1rem 3rem;
            background: rgba(255, 255, 255, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            color: #ffffff;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .login-form .form-control:focus {
            outline: none;
            background: rgba(255, 255, 255, 0.12);
            border-color: #ffffff;
            box-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
        }

        .login-form .form-control::placeholder {
            color: #666666;
        }

        .form-icon {
            position: absolute;
            left: 1rem;
            top: 2.7rem;
            color: #888888;
            font-size: 1.1rem;
        }

        .form-check {
            margin-bottom: 2rem;
            display: flex;
            align-items: center;
            gap: 0.8rem;
        }

        .form-check-input {
            width: 18px;
            height: 18px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 4px;
            background: transparent;
            cursor: pointer;
        }

        .form-check-input:checked {
            background: linear-gradient(45deg, #ffffff, #f0f0f0);
            border-color: #ffffff;
        }

        .form-check-label {
            color: #cccccc;
            font-size: 0.9rem;
            cursor: pointer;
        }

        .btn-login {
            width: 100%;
            padding: 1rem;
            background: linear-gradient(45deg, #ffffff, #f0f0f0);
            color: #1a1a1a;
            border: none;
            border-radius: 15px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-login:hover {
            background: linear-gradient(45deg, #f0f0f0, #e0e0e0);
            transform: translateY(-3px);
            box-shadow: 0 15px 30px rgba(255, 255, 255, 0.2);
        }

        .btn-login:active {
            transform: translateY(-1px);
        }

        .btn-login::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s ease;
        }

        .btn-login:hover::before {
            left: 100%;
        }

        .login-footer {
            text-align: center;
            margin-top: 2.5rem;
            padding-top: 2rem;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .login-footer p {
            color: #666666;
            font-size: 0.9rem;
        }

        .alert {
            background: rgba(244, 67, 54, 0.2);
            color: #f44336;
            border: 1px solid rgba(244, 67, 54, 0.3);
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 2rem;
            backdrop-filter: blur(20px);
        }

        .alert-success {
            background: rgba(76, 175, 80, 0.2);
            color: #4CAF50;
            border-color: rgba(76, 175, 80, 0.3);
        }

        .alert-warning {
            background: rgba(255, 152, 0, 0.2);
            color: #FF9800;
            border-color: rgba(255, 152, 0, 0.3);
        }

        .alert-info {
            background: rgba(33, 150, 243, 0.2);
            color: #2196F3;
            border-color: rgba(33, 150, 243, 0.3);
        }

        .btn-close {
            float: right;
            background: none;
            border: none;
            color: inherit;
            font-size: 1.2rem;
            cursor: pointer;
            padding: 0;
            margin-left: 1rem;
        }

        /* 背景装饰 */
        body::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: 
                radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.05) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.03) 0%, transparent 50%),
                radial-gradient(circle at 40% 60%, rgba(255, 255, 255, 0.02) 0%, transparent 50%);
            animation: backgroundMove 20s ease-in-out infinite;
            z-index: -1;
        }

        @keyframes backgroundMove {
            0%, 100% {
                transform: translate(0, 0) rotate(0deg);
            }
            50% {
                transform: translate(-20px, -20px) rotate(5deg);
            }
        }

        @media (max-width: 480px) {
            .login-container {
                margin: 1rem;
                padding: 2rem;
            }

            .login-header h4 {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h4>管理员登录</h4>
            <p>请输入您的账号和密码</p>
        </div>
        
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }}">
                        {{ message }}
                        <button type="button" class="btn-close" onclick="this.parentElement.style.display='none'">&times;</button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        <form class="login-form" method="post" action="{{ url_for('auth.login') }}">
            <div class="form-group">
                <label for="username" class="form-label">用户名</label>
                <div style="position: relative;">
                    <i class="fas fa-user form-icon"></i>
                    <input type="text" class="form-control" id="username" name="username" placeholder="请输入用户名" required>
                </div>
            </div>
            
            <div class="form-group">
                <label for="password" class="form-label">密码</label>
                <div style="position: relative;">
                    <i class="fas fa-lock form-icon"></i>
                    <input type="password" class="form-control" id="password" name="password" placeholder="请输入密码" required>
                </div>
            </div>
            
            <div class="form-check">
                <input type="checkbox" class="form-check-input" id="remember" name="remember">
                <label class="form-check-label" for="remember">记住我</label>
            </div>
            
            <button type="submit" class="btn-login">
                <i class="fas fa-sign-in-alt" style="margin-right: 0.5rem;"></i>
                登录
            </button>
        </form>
        
        <div class="login-footer">
            <p>Music Player Admin 管理系统</p>
        </div>
    </div>
</body>
</html> 