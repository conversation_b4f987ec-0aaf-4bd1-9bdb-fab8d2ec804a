<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员登录 - Music Player Admin</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: #f8f9fa;
            color: #333333;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .login-container {
            max-width: 450px;
            width: 100%;
            margin: 2rem;
            background: #ffffff;
            border: 1px solid #e9ecef;
            border-radius: 12px;
            padding: 3rem;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            animation: fadeInUp 0.8s ease;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .login-header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .login-header h4 {
            font-weight: 600;
            font-size: 2rem;
            margin-bottom: 0.5rem;
            color: #333333;
        }

        .login-header p {
            color: #666666;
            font-size: 1rem;
        }

        .login-form .form-group {
            margin-bottom: 2rem;
            position: relative;
        }

        .login-form .form-label {
            display: block;
            margin-bottom: 0.8rem;
            color: #495057;
            font-weight: 500;
            font-size: 0.9rem;
        }

        .login-form .form-control {
            width: 100%;
            padding: 1rem 1rem 1rem 3rem;
            background: #ffffff;
            border: 1px solid #ced4da;
            border-radius: 8px;
            color: #495057;
            font-size: 1rem;
        }

        .login-form .form-control:focus {
            outline: none;
            background: #ffffff;
            border-color: #80bdff;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
        }

        .login-form .form-control::placeholder {
            color: #6c757d;
        }

        .form-icon {
            position: absolute;
            left: 1rem;
            top: 2.7rem;
            color: #6c757d;
            font-size: 1.1rem;
        }

        .form-check {
            margin-bottom: 2rem;
            display: flex;
            align-items: center;
            gap: 0.8rem;
        }

        .form-check-input {
            width: 18px;
            height: 18px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            background: #ffffff;
            cursor: pointer;
        }

        .form-check-input:checked {
            background: #007bff;
            border-color: #007bff;
        }

        .form-check-label {
            color: #495057;
            font-size: 0.9rem;
            cursor: pointer;
        }

        .btn-login {
            width: 100%;
            padding: 1rem;
            background: #007bff;
            color: #ffffff;
            border: none;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .btn-login:hover {
            background: #0056b3;
        }

        .login-footer {
            text-align: center;
            margin-top: 2.5rem;
            padding-top: 2rem;
            border-top: 1px solid #e9ecef;
        }

        .login-footer p {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .alert {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
            border-radius: 6px;
            padding: 1rem;
            margin-bottom: 2rem;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border-color: #c3e6cb;
        }

        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border-color: #ffeaa7;
        }

        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border-color: #bee5eb;
        }

        .btn-close {
            float: right;
            background: none;
            border: none;
            color: inherit;
            font-size: 1.2rem;
            cursor: pointer;
            padding: 0;
            margin-left: 1rem;
        }

        @media (max-width: 480px) {
            .login-container {
                margin: 1rem;
                padding: 2rem;
            }

            .login-header h4 {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h4>管理员登录</h4>
            <p>请输入您的账号和密码</p>
        </div>
        
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }}">
                        {{ message }}
                        <button type="button" class="btn-close" onclick="this.parentElement.style.display='none'">&times;</button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        <form class="login-form" method="post" action="{{ url_for('auth.login') }}">
            <div class="form-group">
                <label for="username" class="form-label">用户名</label>
                <div style="position: relative;">
                    <i class="fas fa-user form-icon"></i>
                    <input type="text" class="form-control" id="username" name="username" placeholder="请输入用户名" required>
                </div>
            </div>
            
            <div class="form-group">
                <label for="password" class="form-label">密码</label>
                <div style="position: relative;">
                    <i class="fas fa-lock form-icon"></i>
                    <input type="password" class="form-control" id="password" name="password" placeholder="请输入密码" required>
                </div>
            </div>
            
            <div class="form-check">
                <input type="checkbox" class="form-check-input" id="remember" name="remember">
                <label class="form-check-label" for="remember">记住我</label>
            </div>
            
            <button type="submit" class="btn-login">
                <i class="fas fa-sign-in-alt" style="margin-right: 0.5rem;"></i>
                登录
            </button>
        </form>
        
        <div class="login-footer">
            <p>Music Player Admin 管理系统</p>
        </div>
    </div>
</body>
</html> 