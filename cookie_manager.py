import os
from typing import Dict, List, Optional
from datetime import datetime
import random
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('cookie_manager')

class CookieManager:
    def __init__(self, cookie_file: str = None, db=None):
        """
        初始化Cookie管理器
        :param cookie_file: 传统模式下的cookie文件路径
        :param db: 数据库对象，用于数据库模式
        """
        self.db = db
        self.use_db = db is not None
        
        # 传统文件模式的初始化
        if cookie_file is None and not self.use_db:
            script_dir = os.path.dirname(os.path.abspath(__file__))
            cookie_file = os.path.join(script_dir, 'cookie.txt')
        self.cookie_file = cookie_file
        
        # 缓存最近使用的Cookie
        self.cookie_cache = {}
        self.last_rotation_time = datetime.now()
        self.rotation_interval = 60  # 默认60秒轮换一次

    def read_cookie(self) -> str:
        """读取传统模式下的cookie文件"""
        with open(self.cookie_file, 'r', encoding='utf-8') as f:
            return f.read()

    @staticmethod
    def parse_cookie(text: str) -> Dict[str, str]:
        """解析cookie文本为字典"""
        if not text or not text.strip():
            return {}
        cookie_ = [item.strip().split('=', 1) for item in text.strip().split(';') if item]
        cookie_ = {k.strip(): v.strip() for k, v in cookie_ if len(k.strip()) > 0}
        return cookie_

    def get_cookie(self, quality_level: str = None) -> Dict[str, str]:
        """
        获取cookie，支持数据库模式和传统模式
        :param quality_level: 音质级别，用于选择合适的cookie
        :return: cookie字典
        """
        if not self.use_db:
            # 传统模式
            return self.parse_cookie(self.read_cookie())
        
        # 数据库模式
        from models import CookieStore, SystemConfig
        
        # 检查是否启用Cookie轮换
        enable_rotation = True
        try:
            rotation_config = SystemConfig.query.filter_by(key='cookie_rotation').first()
            if rotation_config and rotation_config.value.lower() == 'false':
                enable_rotation = False
        except Exception as e:
            logger.error(f"获取Cookie轮换配置失败: {str(e)}")
        
        # 根据音质级别选择合适的Cookie
        vip_type = 'svip' if quality_level in ['sky', 'jymaster'] else 'vip'
        
        # 获取所有活跃的Cookie
        try:
            if vip_type == 'svip':
                # SVIP可以使用所有Cookie
                active_cookies = CookieStore.query.filter_by(is_active=True).all()
            else:
                # VIP只能使用VIP级别的Cookie
                active_cookies = CookieStore.query.filter_by(is_active=True, vip_type='vip').all()
            
            if not active_cookies:
                logger.warning(f"没有找到可用的{vip_type}级别Cookie，尝试使用默认Cookie")
                return self.parse_cookie(self.read_cookie())
            
            # 选择Cookie策略
            selected_cookie = None
            
            if enable_rotation:
                # 轮换策略：随机选择一个Cookie
                selected_cookie = random.choice(active_cookies)
            else:
                # 不轮换：选择使用次数最少的Cookie
                selected_cookie = min(active_cookies, key=lambda x: x.use_count)
            
            # 更新Cookie使用记录
            selected_cookie.last_used = datetime.now()
            selected_cookie.use_count += 1
            self.db.session.commit()
            
            logger.info(f"使用Cookie: {selected_cookie.name} (ID: {selected_cookie.id})")
            return self.parse_cookie(selected_cookie.cookie_text)
            
        except Exception as e:
            logger.error(f"从数据库获取Cookie失败: {str(e)}")
            # 出错时使用传统模式的Cookie作为备份
            return self.parse_cookie(self.read_cookie())
    
    def add_cookie(self, name: str, cookie_text: str, vip_type: str = 'vip') -> bool:
        """
        添加新的Cookie到数据库
        :param name: Cookie描述/名称
        :param cookie_text: Cookie文本内容
        :param vip_type: Cookie等级，vip或svip
        :return: 是否添加成功
        """
        if not self.use_db:
            logger.error("未启用数据库模式，无法添加Cookie")
            return False
        
        try:
            from models import CookieStore
            
            # 验证Cookie格式
            cookie_dict = self.parse_cookie(cookie_text)
            if 'MUSIC_U' not in cookie_dict:
                logger.error("Cookie格式无效，缺少MUSIC_U字段")
                return False
            
            # 创建新的Cookie记录
            new_cookie = CookieStore(
                name=name,
                cookie_text=cookie_text,
                vip_type=vip_type,
                is_active=True
            )
            self.db.session.add(new_cookie)
            self.db.session.commit()
            logger.info(f"成功添加Cookie: {name}")
            return True
        except Exception as e:
            logger.error(f"添加Cookie失败: {str(e)}")
            return False
    
    def update_cookie_status(self, cookie_id: int, is_active: bool) -> bool:
        """
        更新Cookie状态
        :param cookie_id: Cookie ID
        :param is_active: 是否激活
        :return: 是否更新成功
        """
        if not self.use_db:
            return False
        
        try:
            from models import CookieStore
            cookie = CookieStore.query.get(cookie_id)
            if cookie:
                cookie.is_active = is_active
                self.db.session.commit()
                logger.info(f"更新Cookie状态: ID {cookie_id}, 状态: {'激活' if is_active else '禁用'}")
                return True
            return False
        except Exception as e:
            logger.error(f"更新Cookie状态失败: {str(e)}")
            return False
    
    def delete_cookie(self, cookie_id: int) -> bool:
        """
        删除Cookie
        :param cookie_id: Cookie ID
        :return: 是否删除成功
        """
        if not self.use_db:
            return False
        
        try:
            from models import CookieStore
            cookie = CookieStore.query.get(cookie_id)
            if cookie:
                self.db.session.delete(cookie)
                self.db.session.commit()
                logger.info(f"删除Cookie: ID {cookie_id}")
                return True
            return False
        except Exception as e:
            logger.error(f"删除Cookie失败: {str(e)}")
            return False
    
    def get_all_cookies(self) -> List[Dict]:
        """
        获取所有Cookie的列表
        :return: Cookie列表
        """
        if not self.use_db:
            return [{'id': 0, 'name': '默认Cookie', 'is_active': True}]
        
        try:
            from models import CookieStore
            cookies = CookieStore.query.all()
            return [cookie.to_dict() for cookie in cookies]
        except Exception as e:
            logger.error(f"获取Cookie列表失败: {str(e)}")
            return []
