import os
import sys
from datetime import datetime
from getpass import getpass

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models import db, Admin, SystemConfig, DEFAULT_CONFIGS, CookieStore


def init_db(app):
    """
    初始化数据库
    :param app: Flask应用实例
    """
    with app.app_context():
        # 创建所有表
        db.create_all()
        
        # 检查是否已有管理员账号
        admin_exists = Admin.query.first() is not None
        if not admin_exists:
            print("=== 创建管理员账号 ===")
            username = input("请输入管理员用户名: ")
            while True:
                password = getpass("请输入管理员密码: ")
                confirm_password = getpass("请再次输入密码确认: ")
                if password == confirm_password:
                    break
                print("两次密码输入不一致，请重新输入")
            
            admin = Admin(username=username)
            admin.set_password(password)
            db.session.add(admin)
            db.session.commit()
            print(f"管理员账号 {username} 创建成功!")
        
        # 初始化系统配置
        for config in DEFAULT_CONFIGS:
            existing = SystemConfig.query.filter_by(key=config['key']).first()
            if not existing:
                new_config = SystemConfig(
                    key=config['key'],
                    value=config['value'],
                    description=config['description']
                )
                db.session.add(new_config)
        
        # 导入默认Cookie
        cookie_exists = CookieStore.query.first() is not None
        if not cookie_exists:
            try:
                # 尝试从cookie.txt导入默认Cookie
                cookie_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'cookie.txt')
                if os.path.exists(cookie_file):
                    with open(cookie_file, 'r', encoding='utf-8') as f:
                        cookie_text = f.read().strip()
                    
                    if cookie_text:
                        default_cookie = CookieStore(
                            name="默认Cookie",
                            cookie_text=cookie_text,
                            is_active=True,
                            vip_type="vip"  # 默认为VIP级别
                        )
                        db.session.add(default_cookie)
                        print("已导入默认Cookie")
            except Exception as e:
                print(f"导入默认Cookie失败: {str(e)}")
        
        db.session.commit()
        print("数据库初始化完成!")


if __name__ == "__main__":
    from main import create_app
    app = create_app()
    init_db(app) 