{% extends "admin/base.html" %}

{% block title %}仪表盘{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">仪表盘</h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            <div class="btn-group me-2">
                <button type="button" class="btn btn-sm btn-outline-secondary refresh-stats">
                    <i class="fas fa-sync-alt"></i> 刷新数据
                </button>
            </div>
        </div>
    </div>

    <!-- 统计卡片 -->
    <div class="row">
        <div class="col-xl-3 col-md-6">
            <div class="stats-card d-flex align-items-center">
                <div class="stats-icon bg-primary text-white">
                    <i class="fas fa-cookie"></i>
                </div>
                <div class="stats-info flex-grow-1">
                    <h5>Cookie总数</h5>
                    <p>系统中的Cookie数量</p>
                </div>
                <div class="stats-value">{{ stats.cookie_count }}</div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="stats-card d-flex align-items-center">
                <div class="stats-icon bg-success text-white">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stats-info flex-grow-1">
                    <h5>活跃Cookie</h5>
                    <p>当前可用的Cookie</p>
                </div>
                <div class="stats-value">{{ stats.active_cookie_count }}</div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="stats-card d-flex align-items-center">
                <div class="stats-icon bg-info text-white">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stats-info flex-grow-1">
                    <h5>今日请求</h5>
                    <p>过去24小时的请求量</p>
                </div>
                <div class="stats-value">{{ stats.today_requests }}</div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="stats-card d-flex align-items-center">
                <div class="stats-icon bg-warning text-white">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="stats-info flex-grow-1">
                    <h5>总请求量</h5>
                    <p>系统总请求数</p>
                </div>
                <div class="stats-value">{{ stats.total_requests }}</div>
            </div>
        </div>
    </div>

    <!-- 图表区域 -->
    <div class="row mt-4">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-chart-bar me-1"></i>
                    最近7天请求量
                </div>
                <div class="card-body">
                    <div id="daily-chart" style="height: 300px;"></div>
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-chart-pie me-1"></i>
                    接口请求分布
                </div>
                <div class="card-body">
                    <div id="endpoint-chart" style="height: 300px;"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 今日请求统计 -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-chart-line me-1"></i>
                    今日请求统计（按小时）
                </div>
                <div class="card-body">
                    <div id="hourly-chart" style="height: 250px;"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 最近访问记录 -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-history me-1"></i>
                    最近访问记录
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>IP地址</th>
                                    <th>接口</th>
                                    <th>方法</th>
                                    <th>状态码</th>
                                    <th>时间</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for log in recent_logs %}
                                <tr>
                                    <td>{{ log.ip_address }}</td>
                                    <td>{{ log.endpoint }}</td>
                                    <td>{{ log.method }}</td>
                                    <td>
                                        {% if log.status_code < 400 %}
                                            <span class="badge bg-success">{{ log.status_code }}</span>
                                        {% elif log.status_code < 500 %}
                                            <span class="badge bg-warning">{{ log.status_code }}</span>
                                        {% else %}
                                            <span class="badge bg-danger">{{ log.status_code }}</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ log.request_time.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 每日请求量图表
        var dailyChart = echarts.init(document.getElementById('daily-chart'));
        
        // 接口请求分布图表
        var endpointChart = echarts.init(document.getElementById('endpoint-chart'));
        
        // 小时请求统计图表
        var hourlyChart = echarts.init(document.getElementById('hourly-chart'));
        
        // 加载数据
        loadChartData();
        
        // 窗口大小变化时重绘图表
        window.addEventListener('resize', function() {
            dailyChart.resize();
            endpointChart.resize();
            hourlyChart.resize();
        });
        
        // 刷新按钮点击事件
        document.querySelector('.refresh-stats').addEventListener('click', function() {
            loadChartData();
        });
        
        // 加载图表数据
        function loadChartData() {
            fetch('{{ url_for("admin.api_stats") }}')
                .then(function(response) { return response.json(); })
                .then(function(data) {
                    // 设置每日请求量图表
                    var dates = data.daily.map(function(item) { return item.date; });
                    var counts = data.daily.map(function(item) { return item.count; });
                    
                    dailyChart.setOption({
                        tooltip: {
                            trigger: 'axis'
                        },
                        xAxis: {
                            type: 'category',
                            data: dates
                        },
                        yAxis: {
                            type: 'value'
                        },
                        series: [{
                            data: counts,
                            type: 'line',
                            smooth: true,
                            lineStyle: {
                                width: 3,
                                color: '#3498db'
                            },
                            areaStyle: {
                                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                                    offset: 0,
                                    color: 'rgba(52, 152, 219, 0.5)'
                                }, {
                                    offset: 1,
                                    color: 'rgba(52, 152, 219, 0.1)'
                                }])
                            }
                        }]
                    });
                    
                    // 设置接口请求分布图表
                    endpointChart.setOption({
                        tooltip: {
                            trigger: 'item',
                            formatter: '{b}: {c} ({d}%)'
                        },
                        legend: {
                            orient: 'vertical',
                            right: 10,
                            top: 'center',
                            formatter: function(name) {
                                return name.length > 10 ? name.substring(0, 10) + '...' : name;
                            }
                        },
                        series: [{
                            name: '接口请求',
                            type: 'pie',
                            radius: ['40%', '70%'],
                            avoidLabelOverlap: false,
                            itemStyle: {
                                borderRadius: 10,
                                borderColor: '#fff',
                                borderWidth: 2
                            },
                            label: {
                                show: false
                            },
                            emphasis: {
                                label: {
                                    show: true,
                                    fontSize: '16',
                                    fontWeight: 'bold'
                                }
                            },
                            labelLine: {
                                show: false
                            },
                            data: data.endpoints
                        }]
                    });
                });
                
            // 设置小时请求统计图表
            var hours = [];
            for (var i = 0; i < 24; i++) {
                hours.push(i + '时');
            }
            
            hourlyChart.setOption({
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                xAxis: {
                    type: 'category',
                    data: hours
                },
                yAxis: {
                    type: 'value'
                },
                series: [{
                    data: {{ hourly_data|tojson }},
                    type: 'bar',
                    itemStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                                offset: 0,
                                color: '#83bff6'
                            }, 
                            {
                                offset: 0.5,
                                color: '#188df0'
                            }, 
                            {
                                offset: 1,
                                color: '#188df0'
                            }
                        ])
                    }
                }]
            });
        }
    });
</script>
{% endblock %} 