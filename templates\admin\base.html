<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}管理面板{% endblock %} - Music Player Admin</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.1.3/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%) !important;
            color: #ffffff !important;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .navbar {
            background: rgba(255, 255, 255, 0.08) !important;
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            padding: 1rem 0;
        }

        .navbar-brand {
            color: #ffffff !important;
            font-weight: 300;
            font-size: 1.5rem;
            background: linear-gradient(45deg, #ffffff, #cccccc);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .navbar-nav .nav-link {
            color: rgba(255, 255, 255, 0.8) !important;
            transition: all 0.3s ease;
        }

        .navbar-nav .nav-link:hover {
            color: #ffffff !important;
        }

        .dropdown-menu {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
        }

        .dropdown-item {
            color: #ffffff;
            transition: all 0.3s ease;
        }

        .dropdown-item:hover {
            background: rgba(255, 255, 255, 0.1);
            color: #ffffff;
        }

        .sidebar {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border-right: 1px solid rgba(255, 255, 255, 0.1);
            min-height: calc(100vh - 80px);
            padding: 2rem 0;
        }

        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 1rem 1.5rem;
            margin: 0.3rem 1rem;
            border-radius: 12px;
            transition: all 0.3s ease;
            border: 1px solid transparent;
        }

        .sidebar .nav-link:hover {
            color: #ffffff;
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.2);
            transform: translateX(5px);
        }

        .sidebar .nav-link.active {
            color: #1a1a1a;
            background: linear-gradient(45deg, #ffffff, #f0f0f0);
            border-color: #ffffff;
            box-shadow: 0 10px 20px rgba(255, 255, 255, 0.2);
        }

        .sidebar .nav-link i {
            margin-right: 12px;
            width: 20px;
            text-align: center;
        }

        .content {
            padding: 2rem;
            min-height: calc(100vh - 80px);
        }

        .card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            margin-bottom: 2rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            overflow: hidden;
        }

        .card-header {
            background: rgba(255, 255, 255, 0.1);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            padding: 1.5rem 2rem;
            font-weight: 600;
            color: #ffffff;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .card-body {
            padding: 2rem;
        }

        .stats-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .stats-card:hover {
            background: rgba(255, 255, 255, 0.08);
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .stats-card .stats-icon {
            width: 60px;
            height: 60px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1rem;
            font-size: 1.5rem;
            background: linear-gradient(45deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
        }

        .stats-card .stats-value {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: #ffffff;
        }

        .stats-card .stats-label {
            color: #cccccc;
            font-size: 1rem;
            margin-bottom: 0.5rem;
        }

        .stats-card .stats-desc {
            color: #888888;
            font-size: 0.9rem;
        }

        .btn {
            border-radius: 12px;
            padding: 0.8rem 1.5rem;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
        }

        .btn-primary {
            background: linear-gradient(45deg, #ffffff, #f0f0f0);
            color: #1a1a1a;
        }

        .btn-primary:hover {
            background: linear-gradient(45deg, #f0f0f0, #e0e0e0);
            color: #1a1a1a;
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(255, 255, 255, 0.2);
        }

        .btn-outline-primary {
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: #ffffff;
            background: transparent;
        }

        .btn-outline-primary:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.5);
            color: #ffffff;
        }

        .btn-success {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
        }

        .btn-success:hover {
            background: linear-gradient(45deg, #45a049, #4CAF50);
            color: white;
            transform: translateY(-2px);
        }

        .btn-warning {
            background: linear-gradient(45deg, #FF9800, #F57C00);
            color: white;
        }

        .btn-warning:hover {
            background: linear-gradient(45deg, #F57C00, #FF9800);
            color: white;
            transform: translateY(-2px);
        }

        .btn-danger {
            background: linear-gradient(45deg, #f44336, #d32f2f);
            color: white;
        }

        .btn-danger:hover {
            background: linear-gradient(45deg, #d32f2f, #f44336);
            color: white;
            transform: translateY(-2px);
        }

        .table {
            color: #ffffff;
            background: transparent;
        }

        .table thead th {
            border-bottom: 2px solid rgba(255, 255, 255, 0.2);
            color: #cccccc;
            font-weight: 600;
            padding: 1rem;
        }

        .table tbody td {
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            padding: 1rem;
            vertical-align: middle;
        }

        .table tbody tr:hover {
            background: rgba(255, 255, 255, 0.05);
        }

        .form-control {
            background: rgba(255, 255, 255, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            color: #ffffff;
            padding: 0.8rem 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            background: rgba(255, 255, 255, 0.12);
            border-color: #ffffff;
            color: #ffffff;
            box-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
        }

        .form-control::placeholder {
            color: #888888;
        }

        .form-select {
            background: rgba(255, 255, 255, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            color: #ffffff;
            padding: 0.8rem 1rem;
        }

        .form-select:focus {
            background: rgba(255, 255, 255, 0.12);
            border-color: #ffffff;
            color: #ffffff;
        }

        .form-select option {
            background: #2d2d2d;
            color: #ffffff;
        }

        .form-label {
            color: #cccccc;
            font-weight: 500;
            margin-bottom: 0.5rem;
        }

        .alert {
            border-radius: 12px;
            border: none;
            backdrop-filter: blur(20px);
        }

        .alert-success {
            background: rgba(76, 175, 80, 0.2);
            color: #4CAF50;
            border: 1px solid rgba(76, 175, 80, 0.3);
        }

        .alert-danger {
            background: rgba(244, 67, 54, 0.2);
            color: #f44336;
            border: 1px solid rgba(244, 67, 54, 0.3);
        }

        .alert-warning {
            background: rgba(255, 152, 0, 0.2);
            color: #FF9800;
            border: 1px solid rgba(255, 152, 0, 0.3);
        }

        .alert-info {
            background: rgba(33, 150, 243, 0.2);
            color: #2196F3;
            border: 1px solid rgba(33, 150, 243, 0.3);
        }

        .badge {
            border-radius: 8px;
            padding: 0.4rem 0.8rem;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .bg-success {
            background: linear-gradient(45deg, #4CAF50, #45a049) !important;
        }

        .bg-danger {
            background: linear-gradient(45deg, #f44336, #d32f2f) !important;
        }

        .bg-warning {
            background: linear-gradient(45deg, #FF9800, #F57C00) !important;
        }

        .bg-info {
            background: linear-gradient(45deg, #2196F3, #1976D2) !important;
        }

        .bg-secondary {
            background: linear-gradient(45deg, #666666, #555555) !important;
        }

        .bg-primary {
            background: linear-gradient(45deg, #ffffff, #f0f0f0) !important;
            color: #1a1a1a !important;
        }

        .pagination .page-link {
            background: rgba(255, 255, 255, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: #ffffff;
            margin: 0 0.2rem;
            border-radius: 8px;
        }

        .pagination .page-link:hover {
            background: rgba(255, 255, 255, 0.15);
            border-color: rgba(255, 255, 255, 0.3);
            color: #ffffff;
        }

        .pagination .page-item.active .page-link {
            background: linear-gradient(45deg, #ffffff, #f0f0f0);
            border-color: #ffffff;
            color: #1a1a1a;
        }

        .modal-content {
            background: rgba(45, 45, 45, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            color: #ffffff;
        }

        .modal-header {
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .modal-footer {
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .navbar-toggler {
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .navbar-toggler-icon {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.8%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
        }

        @media (max-width: 768px) {
            .sidebar {
                min-width: 100%;
                min-height: auto;
            }

            .content {
                padding: 1rem;
            }

            .stats-card {
                padding: 1.5rem;
            }
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .card {
            animation: fadeIn 0.6s ease;
        }

        .stats-card {
            animation: fadeIn 0.6s ease;
        }
    </style>
    {% block extra_css %}{% endblock %}
</head>
<body>
    <nav class="navbar navbar-expand-lg fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('admin.dashboard') }}">Music Player Admin</a>
                         <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user me-2"></i>{{ current_user.username }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="{{ url_for('admin.profile') }}"><i class="fas fa-user-cog me-2"></i>个人设置</a></li>
                            <li><hr class="dropdown-divider" style="border-color: rgba(255,255,255,0.2);"></li>
                            <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}"><i class="fas fa-sign-out-alt me-2"></i>退出登录</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    
    <div class="container-fluid" style="margin-top: 80px;">
        <div class="row">
            <div class="col-md-3 col-lg-2 d-md-block sidebar collapse" id="sidebar">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'admin.dashboard' %}active{% endif %}" href="{{ url_for('admin.dashboard') }}">
                            <i class="fas fa-tachometer-alt"></i>仪表盘
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'admin.cookies' %}active{% endif %}" href="{{ url_for('admin.cookies') }}">
                            <i class="fas fa-cookie"></i>Cookie管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'admin.settings' %}active{% endif %}" href="{{ url_for('admin.settings') }}">
                            <i class="fas fa-cogs"></i>系统设置
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'admin.logs' %}active{% endif %}" href="{{ url_for('admin.logs') }}">
                            <i class="fas fa-history"></i>访问日志
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('index') }}" target="_blank">
                            <i class="fas fa-home"></i>前台首页
                        </a>
                    </li>
                </ul>
            </div>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 content">
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close" style="filter: invert(1);"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}
                
                {% block content %}{% endblock %}
            </main>
        </div>
    </div>
    
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.1.3/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/echarts/5.3.2/echarts.min.js"></script>
    <script>
        // Bootstrap下拉菜单已经自动处理
    </script>
    {% block extra_js %}{% endblock %}
</body>
</html> 