<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}管理面板{% endblock %} - Music Player Admin</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.1.3/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: #f8f9fa !important;
            color: #333333 !important;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .navbar {
            background: #ffffff !important;
            border-bottom: 1px solid #e9ecef;
            padding: 1rem 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            color: #333333 !important;
            font-weight: 600;
            font-size: 1.5rem;
        }

        .navbar-nav .nav-link {
            color: #666666 !important;
        }

        .dropdown-menu {
            background: #ffffff;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .dropdown-item {
            color: #333333;
        }

        .dropdown-item:hover {
            background: #f8f9fa;
            color: #333333;
        }

        .sidebar {
            background: #ffffff;
            border-right: 1px solid #e9ecef;
            min-height: calc(100vh - 80px);
            padding: 2rem 0;
        }

        .sidebar .nav-link {
            color: #666666;
            padding: 1rem 1.5rem;
            margin: 0.3rem 1rem;
            border-radius: 8px;
            border: 1px solid transparent;
        }

        .sidebar .nav-link.active {
            color: #333333;
            background: #f8f9fa;
            border-color: #dee2e6;
        }

        .sidebar .nav-link i {
            margin-right: 12px;
            width: 20px;
            text-align: center;
        }

        .content {
            padding: 2rem;
            min-height: calc(100vh - 80px);
        }

        .card {
            background: #ffffff;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            margin-bottom: 2rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .card-header {
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            padding: 1.5rem 2rem;
            font-weight: 600;
            color: #333333;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .card-body {
            padding: 2rem;
        }

        .stats-card {
            background: #ffffff;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 2rem;
            margin-bottom: 2rem;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .stats-card:hover {
            background: #f8f9fa;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .stats-card .stats-icon {
            width: 60px;
            height: 60px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1rem;
            font-size: 1.5rem;
        }

        .stats-card .stats-value {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: #333333;
        }

        .stats-card .stats-label {
            color: #666666;
            font-size: 1rem;
            margin-bottom: 0.5rem;
        }

        .stats-card .stats-desc {
            color: #999999;
            font-size: 0.9rem;
        }

        .btn {
            border-radius: 6px;
            padding: 0.8rem 1.5rem;
            font-weight: 500;
            border: 1px solid transparent;
        }

        .btn-primary {
            background: #007bff;
            color: #ffffff;
            border-color: #007bff;
        }

        .btn-primary:hover {
            background: #0056b3;
            border-color: #0056b3;
            color: #ffffff;
        }

        .btn-outline-primary {
            border: 1px solid #007bff;
            color: #007bff;
            background: transparent;
        }

        .btn-outline-primary:hover {
            background: #007bff;
            color: #ffffff;
        }

        .btn-outline-secondary {
            border: 1px solid #6c757d;
            color: #6c757d;
            background: transparent;
        }

        .btn-outline-secondary:hover {
            background: #6c757d;
            color: #ffffff;
        }

        .btn-success {
            background: #28a745;
            color: #ffffff;
            border-color: #28a745;
        }

        .btn-success:hover {
            background: #1e7e34;
            border-color: #1e7e34;
            color: #ffffff;
        }

        .btn-warning {
            background: #ffc107;
            color: #212529;
            border-color: #ffc107;
        }

        .btn-warning:hover {
            background: #e0a800;
            border-color: #e0a800;
            color: #212529;
        }

        .btn-danger {
            background: #dc3545;
            color: #ffffff;
            border-color: #dc3545;
        }

        .btn-danger:hover {
            background: #c82333;
            border-color: #c82333;
            color: #ffffff;
        }

        .table {
            color: #333333;
            background: transparent;
        }

        .table thead th {
            border-bottom: 2px solid #dee2e6;
            color: #495057;
            font-weight: 600;
            padding: 1rem;
        }

        .table tbody td {
            border-bottom: 1px solid #dee2e6;
            padding: 1rem;
            vertical-align: middle;
        }

        .form-control {
            background: #ffffff;
            border: 1px solid #ced4da;
            border-radius: 6px;
            color: #495057;
            padding: 0.8rem 1rem;
        }

        .form-control:focus {
            background: #ffffff;
            border-color: #80bdff;
            color: #495057;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
        }

        .form-control::placeholder {
            color: #6c757d;
        }

        .form-select {
            background: #ffffff;
            border: 1px solid #ced4da;
            border-radius: 6px;
            color: #495057;
            padding: 0.8rem 1rem;
        }

        .form-select:focus {
            background: #ffffff;
            border-color: #80bdff;
            color: #495057;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
        }

        .form-select option {
            background: #ffffff;
            color: #495057;
        }

        .form-label {
            color: #495057;
            font-weight: 500;
            margin-bottom: 0.5rem;
        }

        .alert {
            border-radius: 6px;
            border: 1px solid transparent;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border-color: #c3e6cb;
        }

        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border-color: #f5c6cb;
        }

        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border-color: #ffeaa7;
        }

        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border-color: #bee5eb;
        }

        .badge {
            border-radius: 4px;
            padding: 0.4rem 0.8rem;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .bg-success {
            background: #28a745 !important;
            color: #ffffff !important;
        }

        .bg-danger {
            background: #dc3545 !important;
            color: #ffffff !important;
        }

        .bg-warning {
            background: #ffc107 !important;
            color: #212529 !important;
        }

        .bg-info {
            background: #17a2b8 !important;
            color: #ffffff !important;
        }

        .bg-secondary {
            background: #6c757d !important;
            color: #ffffff !important;
        }

        .bg-primary {
            background: #007bff !important;
            color: #ffffff !important;
        }

        .pagination .page-link {
            background: #ffffff;
            border: 1px solid #dee2e6;
            color: #007bff;
            margin: 0 0.2rem;
            border-radius: 4px;
        }

        .pagination .page-link:hover {
            background: #e9ecef;
            border-color: #dee2e6;
            color: #0056b3;
        }

        .pagination .page-item.active .page-link {
            background: #007bff;
            border-color: #007bff;
            color: #ffffff;
        }

        .modal-content {
            background: #ffffff;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            color: #333333;
        }

        .modal-header {
            border-bottom: 1px solid #dee2e6;
        }

        .modal-footer {
            border-top: 1px solid #dee2e6;
        }

        .navbar-toggler {
            border: 1px solid #dee2e6;
        }

        .navbar-toggler-icon {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%2833, 37, 41, 0.75%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
        }

        @media (max-width: 768px) {
            .sidebar {
                min-width: 100%;
                min-height: auto;
            }

            .content {
                padding: 1rem;
            }

            .stats-card {
                padding: 1.5rem;
            }
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .card {
            animation: fadeIn 0.6s ease;
        }

        .stats-card {
            animation: fadeIn 0.6s ease;
        }
    </style>
    {% block extra_css %}{% endblock %}
</head>
<body>
    <nav class="navbar navbar-expand-lg fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('admin.dashboard') }}">Music Player Admin</a>
                         <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user me-2"></i>{{ current_user.username }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="{{ url_for('admin.profile') }}"><i class="fas fa-user-cog me-2"></i>个人设置</a></li>
                            <li><hr class="dropdown-divider" style="border-color: rgba(255,255,255,0.2);"></li>
                            <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}"><i class="fas fa-sign-out-alt me-2"></i>退出登录</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    
    <div class="container-fluid" style="margin-top: 80px;">
        <div class="row">
            <div class="col-md-3 col-lg-2 d-md-block sidebar collapse" id="sidebar">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'admin.dashboard' %}active{% endif %}" href="{{ url_for('admin.dashboard') }}">
                            <i class="fas fa-tachometer-alt"></i>仪表盘
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'admin.cookies' %}active{% endif %}" href="{{ url_for('admin.cookies') }}">
                            <i class="fas fa-cookie"></i>Cookie管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'admin.settings' %}active{% endif %}" href="{{ url_for('admin.settings') }}">
                            <i class="fas fa-cogs"></i>系统设置
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'admin.logs' %}active{% endif %}" href="{{ url_for('admin.logs') }}">
                            <i class="fas fa-history"></i>访问日志
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('index') }}" target="_blank">
                            <i class="fas fa-home"></i>前台首页
                        </a>
                    </li>
                </ul>
            </div>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 content">
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close" style="filter: invert(1);"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}
                
                {% block content %}{% endblock %}
            </main>
        </div>
    </div>
    
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.1.3/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/echarts/5.3.2/echarts.min.js"></script>
    <script>
        // Bootstrap下拉菜单已经自动处理
    </script>
    {% block extra_js %}{% endblock %}
</body>
</html> 