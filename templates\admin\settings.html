{% extends "admin/base.html" %}

{% block title %}系统设置{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">系统设置</h1>
    </div>

    <form method="post" action="{{ url_for('admin.update_settings') }}">
        <!-- 访问频率限制设置 -->
        <div class="card mb-4">
            <div class="card-header">
                <i class="fas fa-tachometer-alt me-1"></i>
                访问频率限制
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> 访问频率限制格式：数字/单位，例如：60/minute、100/hour、1000/day
                </div>
                
                <div class="row">
                    {% for config in rate_limits %}
                    <div class="col-md-6 col-lg-4 mb-3">
                        <label for="config_{{ config.key }}" class="form-label">{{ config.description }}</label>
                        <input type="text" class="form-control" id="config_{{ config.key }}" name="config_{{ config.key }}" value="{{ config.value }}">
                        <div class="form-text">配置键：{{ config.key }}</div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- 音质限制设置 -->
        <div class="card mb-4">
            <div class="card-header">
                <i class="fas fa-music me-1"></i>
                音质限制设置
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> 音质级别：standard(标准)、exhigh(极高)、lossless(无损)、hires(Hi-Res)、jyeffect(高清环绕声)、sky(沉浸环绕声)、jymaster(超清母带)
                </div>
                
                <div class="row">
                    {% for config in qualities %}
                    <div class="col-md-6 mb-3">
                        <label for="config_{{ config.key }}" class="form-label">{{ config.description }}</label>
                        {% if config.key == 'allowed_qualities' %}
                        <input type="text" class="form-control" id="config_{{ config.key }}" name="config_{{ config.key }}" value="{{ config.value }}">
                        <div class="form-text">多个音质用逗号分隔</div>
                        {% else %}
                        <select class="form-select" id="config_{{ config.key }}" name="config_{{ config.key }}">
                            <option value="standard" {% if config.value == 'standard' %}selected{% endif %}>标准音质</option>
                            <option value="exhigh" {% if config.value == 'exhigh' %}selected{% endif %}>极高音质</option>
                            <option value="lossless" {% if config.value == 'lossless' %}selected{% endif %}>无损音质</option>
                            <option value="hires" {% if config.value == 'hires' %}selected{% endif %}>Hi-Res音质</option>
                            <option value="jyeffect" {% if config.value == 'jyeffect' %}selected{% endif %}>高清环绕声</option>
                            <option value="sky" {% if config.value == 'sky' %}selected{% endif %}>沉浸环绕声</option>
                            <option value="jymaster" {% if config.value == 'jymaster' %}selected{% endif %}>超清母带</option>
                        </select>
                        {% endif %}
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- 其他设置 -->
        <div class="card mb-4">
            <div class="card-header">
                <i class="fas fa-cogs me-1"></i>
                其他设置
            </div>
            <div class="card-body">
                <div class="row">
                    {% for config in others %}
                    <div class="col-md-6 mb-3">
                        <label for="config_{{ config.key }}" class="form-label">{{ config.description }}</label>
                        {% if config.key == 'cookie_rotation' %}
                        <select class="form-select" id="config_{{ config.key }}" name="config_{{ config.key }}">
                            <option value="true" {% if config.value == 'true' %}selected{% endif %}>启用</option>
                            <option value="false" {% if config.value == 'false' %}selected{% endif %}>禁用</option>
                        </select>
                        {% else %}
                        <input type="text" class="form-control" id="config_{{ config.key }}" name="config_{{ config.key }}" value="{{ config.value }}">
                        {% endif %}
                        <div class="form-text">配置键：{{ config.key }}</div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>

        <div class="text-end mb-4">
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save"></i> 保存设置
            </button>
        </div>
    </form>
</div>
{% endblock %} 