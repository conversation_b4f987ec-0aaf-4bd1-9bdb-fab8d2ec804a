# 网易云音乐解析 API 文档

## 概述

本API服务提供网易云音乐的解析功能，支持单曲、歌单、专辑的获取和音乐搜索。服务采用RESTful风格，支持JSON格式数据交互。

### 主要特性

- 🎵 **单曲解析**：支持多种音质，包括标准、极高、无损、Hi-Res、环绕声等
- 🔍 **音乐搜索**：基于关键词搜索歌曲、歌手、专辑
- 📃 **歌单解析**：获取完整歌单信息和歌曲列表
- 💿 **专辑解析**：获取专辑详情和所有收录歌曲
- 🛡️ **访问控制**：支持频率限制和音质限制
- 📊 **访问日志**：完整的API调用记录
- 🌐 **CORS支持**：支持跨域请求

## 基础信息

### 服务地址
```
http://your-domain.com/
```

### 支持的请求方法
- `GET`
- `POST`

### 响应格式
- `application/json`
- `text/html` (仅限特定接口)
- `redirect` (仅限下载模式)

### CORS支持
所有API接口均支持跨域请求，已设置以下CORS头：
```
Access-Control-Allow-Origin: *
Access-Control-Allow-Headers: Content-Type,Authorization
Access-Control-Allow-Methods: GET,POST,OPTIONS
```

## 音质说明

### 音质级别参数

| 参数值 | 音质名称 | 说明 | 会员要求 |
|--------|----------|------|----------|
| `standard` | 标准音质 | 128kbps | 无 |
| `exhigh` | 极高音质 | 320kbps | 黑胶VIP |
| `lossless` | 无损音质 | FLAC | 黑胶VIP |
| `hires` | Hi-Res音质 | 高解析度音频 | 黑胶VIP |
| `jyeffect` | 高清环绕声 | 环绕声效果 | 黑胶VIP |
| `sky` | 沉浸环绕声 | 沉浸式音效 | 黑胶SVIP |
| `jymaster` | 超清母带 | 母带音质 | 黑胶SVIP |

> **注意**：系统管理员可以通过后台设置限制可用的音质级别。

## API 接口

### 1. 系统配置接口

#### 获取系统配置
获取当前系统允许的音质列表和默认音质设置。

**接口地址**
```
GET /api/config
```

**响应示例**
```json
{
  "allowed_qualities": ["standard", "exhigh", "lossless", "hires"],
  "default_quality": "lossless"
}
```

**响应参数说明**

| 参数 | 类型 | 说明 |
|------|------|------|
| allowed_qualities | array | 系统允许使用的音质列表 |
| default_quality | string | 系统默认音质 |

---

### 2. 单曲解析接口

#### 解析单曲信息
根据歌曲ID或网易云链接获取歌曲的详细信息和播放链接。

**接口地址**
```
GET /Song_V1
POST /Song_V1
```

**请求参数**

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| ids | string | 否 | 歌曲ID，与url二选一 |
| url | string | 否 | 网易云音乐歌曲链接，与ids二选一 |
| level | string | 是 | 音质参数，参考音质说明 |
| type | string | 是 | 响应类型：`json`、`text`、`down` |

**请求示例**
```bash
# GET 请求
curl "http://your-domain.com/Song_V1?ids=1234567&level=lossless&type=json"

# POST 请求
curl -X POST "http://your-domain.com/Song_V1" \
  -d "url=https://music.163.com/song?id=1234567" \
  -d "level=lossless" \
  -d "type=json"
```

**响应类型说明**

##### type=json（推荐）
返回完整的JSON格式歌曲信息。

**响应示例**
```json
{
  "status": 200,
  "name": "歌曲名称",
  "pic": "https://...",
  "ar_name": "歌手名称",
  "al_name": "专辑名称", 
  "level": "无损音质",
  "size": "15.2MB",
  "url": "https://...",
  "lyric": "[00:00.00]歌词内容...",
  "tlyric": "[00:00.00]翻译歌词..."
}
```

**响应参数说明**

| 参数 | 类型 | 说明 |
|------|------|------|
| status | number | 状态码，200表示成功 |
| name | string | 歌曲名称 |
| pic | string | 专辑封面图片URL |
| ar_name | string | 歌手名称 |
| al_name | string | 专辑名称 |
| level | string | 实际音质 |
| size | string | 文件大小 |
| url | string | 音频文件URL（HTTPS） |
| lyric | string | 歌词内容（LRC格式） |
| tlyric | string | 翻译歌词（如有） |

##### type=text
返回HTML格式的歌曲信息，适用于网页展示。

##### type=down
直接重定向到音频文件下载地址。

**错误响应**
```json
{
  "status": 400,
  "msg": "错误信息"
}
```

**访问限制**
- 默认：30次/分钟
- 可通过管理员后台调整

---

### 3. 音乐搜索接口

#### 搜索音乐
根据关键词搜索网易云音乐中的歌曲。

**接口地址**
```
GET /Search
POST /Search
```

**请求参数**

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| keywords | string | 是 | 搜索关键词 |
| limit | number | 否 | 返回数量，默认10，最大100 |

**请求示例**
```bash
# GET 请求
curl "http://your-domain.com/Search?keywords=周杰伦&limit=20"

# POST 请求
curl -X POST "http://your-domain.com/Search" \
  -d "keywords=告白气球" \
  -d "limit=10"
```

**响应示例**
```json
{
  "status": 200,
  "result": [
    {
      "id": "1234567",
      "name": "告白气球",
      "artists": "周杰伦",
      "album": "周杰伦的床边故事",
      "picUrl": "https://..."
    }
  ]
}
```

**响应参数说明**

| 参数 | 类型 | 说明 |
|------|------|------|
| status | number | 状态码，200表示成功 |
| result | array | 搜索结果数组 |
| result[].id | string | 歌曲ID |
| result[].name | string | 歌曲名称 |
| result[].artists | string | 歌手名称（多个歌手用/分隔） |
| result[].album | string | 专辑名称 |
| result[].picUrl | string | 专辑封面URL |

**访问限制**
- 默认：20次/分钟

---

### 4. 歌单解析接口

#### 获取歌单详情
根据歌单ID获取歌单信息和完整歌曲列表。

**接口地址**
```
GET /Playlist
POST /Playlist
```

**请求参数**

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| id | string | 是 | 歌单ID |

**请求示例**
```bash
# GET 请求
curl "http://your-domain.com/Playlist?id=123456789"

# POST 请求
curl -X POST "http://your-domain.com/Playlist" \
  -d "id=123456789"
```

**响应示例**
```json
{
  "status": 200,
  "playlist": {
    "id": "123456789",
    "name": "歌单名称",
    "coverImgUrl": "https://...",
    "creator": "创建者昵称",
    "trackCount": 50,
    "description": "歌单描述",
    "tracks": [
      {
        "id": "1234567",
        "name": "歌曲名称",
        "artists": "歌手名称",
        "album": "专辑名称",
        "picUrl": "https://..."
      }
    ]
  }
}
```

**响应参数说明**

| 参数 | 类型 | 说明 |
|------|------|------|
| status | number | 状态码，200表示成功 |
| playlist | object | 歌单信息对象 |
| playlist.id | string | 歌单ID |
| playlist.name | string | 歌单名称 |
| playlist.coverImgUrl | string | 歌单封面URL |
| playlist.creator | string | 创建者昵称 |
| playlist.trackCount | number | 歌曲总数 |
| playlist.description | string | 歌单描述 |
| playlist.tracks | array | 歌曲列表 |

**访问限制**
- 默认：10次/分钟

---

### 5. 专辑解析接口

#### 获取专辑详情
根据专辑ID获取专辑信息和完整歌曲列表。

**接口地址**
```
GET /Album
POST /Album
```

**请求参数**

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| id | string | 是 | 专辑ID |

**请求示例**
```bash
# GET 请求
curl "http://your-domain.com/Album?id=123456"

# POST 请求
curl -X POST "http://your-domain.com/Album" \
  -d "id=123456"
```

**响应示例**
```json
{
  "status": 200,
  "album": {
    "id": "123456",
    "name": "专辑名称",
    "coverImgUrl": "https://...",
    "artist": "歌手名称",
    "publishTime": 1234567890000,
    "description": "专辑描述",
    "songs": [
      {
        "id": "1234567",
        "name": "歌曲名称",
        "artists": "歌手名称",
        "album": "专辑名称",
        "picUrl": "https://..."
      }
    ]
  }
}
```

**响应参数说明**

| 参数 | 类型 | 说明 |
|------|------|------|
| status | number | 状态码，200表示成功 |
| album | object | 专辑信息对象 |
| album.id | string | 专辑ID |
| album.name | string | 专辑名称 |
| album.coverImgUrl | string | 专辑封面URL |
| album.artist | string | 歌手名称 |
| album.publishTime | number | 发布时间戳 |
| album.description | string | 专辑描述 |
| album.songs | array | 歌曲列表 |

**访问限制**
- 默认：10次/分钟

## 错误代码说明

### HTTP状态码

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 429 | 访问频率超限 |
| 500 | 服务器内部错误 |

### 业务错误码

当HTTP状态码为400或500时，响应体包含错误信息：

```json
{
  "status": 400,
  "msg": "具体错误信息"
}
```

常见错误信息：

| 错误信息 | 说明 | 解决方案 |
|----------|------|----------|
| 必须提供 ids 或 url 参数 | 单曲解析缺少歌曲标识 | 提供歌曲ID或网易云链接 |
| level参数为空 | 缺少音质参数 | 提供有效的音质参数 |
| type参数为空 | 缺少响应类型参数 | 提供type参数(json/text/down) |
| 必须提供 keywords 参数 | 搜索缺少关键词 | 提供搜索关键词 |
| 信息获取不完整 | 歌曲解析失败 | 检查歌曲ID是否正确，或稍后重试 |
| 服务异常 | 服务器内部错误 | 稍后重试，或联系管理员 |

## 使用示例

### 1. JavaScript/Ajax示例

```javascript
// 搜索音乐
fetch('http://your-domain.com/Search?keywords=周杰伦&limit=10')
  .then(response => response.json())
  .then(data => {
    if (data.status === 200) {
      console.log('搜索结果:', data.result);
    }
  });

// 获取单曲信息
fetch('http://your-domain.com/Song_V1', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/x-www-form-urlencoded',
  },
  body: 'ids=1234567&level=lossless&type=json'
})
  .then(response => response.json())
  .then(data => {
    if (data.status === 200) {
      console.log('歌曲信息:', data);
      // 播放音频
      const audio = new Audio(data.url);
      audio.play();
    }
  });
```

### 2. Python示例

```python
import requests

# 搜索音乐
response = requests.get('http://your-domain.com/Search', params={
    'keywords': '告白气球',
    'limit': 10
})
data = response.json()
if data['status'] == 200:
    print(f"找到 {len(data['result'])} 首歌曲")

# 获取单曲信息
response = requests.post('http://your-domain.com/Song_V1', data={
    'ids': '1234567',
    'level': 'lossless', 
    'type': 'json'
})
data = response.json()
if data['status'] == 200:
    print(f"歌曲: {data['name']}")
    print(f"歌手: {data['ar_name']}")
    print(f"播放链接: {data['url']}")
```

### 3. cURL示例

```bash
# 搜索音乐
curl "http://your-domain.com/Search?keywords=周杰伦&limit=5"

# 获取单曲信息（JSON格式）
curl -X POST "http://your-domain.com/Song_V1" \
  -d "ids=1234567" \
  -d "level=lossless" \
  -d "type=json"

# 直接下载音频文件
curl -X POST "http://your-domain.com/Song_V1" \
  -d "url=https://music.163.com/song?id=1234567" \
  -d "level=lossless" \
  -d "type=down" \
  -L -o "song.flac"

# 获取歌单信息
curl "http://your-domain.com/Playlist?id=123456789"
```

## 最佳实践

### 1. 访问频率控制
- 建议在客户端实现请求缓存，避免重复请求
- 合理设置请求间隔，避免触发频率限制
- 监控API响应，当收到429状态码时适当延缓请求

### 2. 错误处理
- 始终检查响应的status字段
- 实现适当的错误重试机制
- 对用户友好的错误提示

### 3. 音质选择
- 首先调用`/api/config`获取允许的音质列表
- 根据用户网络环境和设备能力选择合适音质
- 为不同用户群体提供音质选择选项

### 4. 性能优化
- 使用HTTP连接池复用连接
- 实现客户端缓存机制
- 并发请求时控制并发数量

## 管理员配置

系统提供管理员后台，可以配置：

- **访问频率限制**：调整各接口的访问频率
- **音质限制**：控制允许使用的音质级别
- **Cookie管理**：管理网易云账号Cookie，支持不同VIP等级
- **访问日志**：查看详细的API调用记录

访问管理员后台：`http://your-domain.com/admin/login`

## 技术支持

如需技术支持或反馈问题，请通过以下方式联系：

- GitHub Issues: [项目地址]
- 博客: [博客地址]

---

**免责声明**：本API仅供学习和研究使用，请遵守网易云音乐的使用条款和相关法律法规。 