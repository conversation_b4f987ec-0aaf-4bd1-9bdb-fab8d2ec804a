{% extends "admin/base.html" %}

{% block title %}访问日志{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">访问日志</h1>
    </div>

    <!-- 过滤器 -->
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-filter me-1"></i>
            过滤条件
        </div>
        <div class="card-body">
            <form method="get" action="{{ url_for('admin.logs') }}">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label for="ip" class="form-label">IP地址</label>
                        <input type="text" class="form-control" id="ip" name="ip" value="{{ filters.ip }}">
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="endpoint" class="form-label">接口</label>
                        <select class="form-select" id="endpoint" name="endpoint">
                            <option value="">全部</option>
                            {% for ep in endpoints %}
                            <option value="{{ ep }}" {% if filters.endpoint == ep %}selected{% endif %}>{{ ep }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="start_date" class="form-label">开始日期</label>
                        <input type="date" class="form-control" id="start_date" name="start_date" value="{{ filters.start_date }}">
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="end_date" class="form-label">结束日期</label>
                        <input type="date" class="form-control" id="end_date" name="end_date" value="{{ filters.end_date }}">
                    </div>
                </div>
                <div class="text-end">
                    <a href="{{ url_for('admin.logs') }}" class="btn btn-outline-secondary me-2">重置</a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> 搜索
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- 日志列表 -->
    <div class="card">
        <div class="card-header">
            <i class="fas fa-history me-1"></i>
            访问记录
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>IP地址</th>
                            <th>接口</th>
                            <th>方法</th>
                            <th>状态码</th>
                            <th>请求参数</th>
                            <th>时间</th>
                            <th>用户代理</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for log in logs %}
                        <tr>
                            <td>{{ log.id }}</td>
                            <td>{{ log.ip_address }}</td>
                            <td>{{ log.endpoint }}</td>
                            <td>{{ log.method }}</td>
                            <td>
                                {% if log.status_code < 400 %}
                                    <span class="badge bg-success">{{ log.status_code }}</span>
                                {% elif log.status_code < 500 %}
                                    <span class="badge bg-warning">{{ log.status_code }}</span>
                                {% else %}
                                    <span class="badge bg-danger">{{ log.status_code }}</span>
                                {% endif %}
                            </td>
                            <td>
                                <button type="button" class="btn btn-sm btn-outline-info view-params" data-bs-toggle="modal" data-bs-target="#paramsModal" data-params="{{ log.params|tojson|replace('"', '&quot;') }}">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </td>
                            <td>{{ log.request_time.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                            <td>
                                <span class="d-inline-block text-truncate" style="max-width: 150px;" title="{{ log.user_agent }}">
                                    {{ log.user_agent }}
                                </span>
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="8" class="text-center">暂无访问记录</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            {% if pagination.pages > 1 %}
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center">
                    <li class="page-item {% if pagination.page == 1 %}disabled{% endif %}">
                        <a class="page-link" href="{{ url_for('admin.logs', page=pagination.prev_num, ip=filters.ip, endpoint=filters.endpoint, start_date=filters.start_date, end_date=filters.end_date) }}">上一页</a>
                    </li>
                    
                    {% for page in pagination.iter_pages(left_edge=2, left_current=2, right_current=3, right_edge=2) %}
                        {% if page %}
                            <li class="page-item {% if page == pagination.page %}active{% endif %}">
                                <a class="page-link" href="{{ url_for('admin.logs', page=page, ip=filters.ip, endpoint=filters.endpoint, start_date=filters.start_date, end_date=filters.end_date) }}">{{ page }}</a>
                            </li>
                        {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                        {% endif %}
                    {% endfor %}
                    
                    <li class="page-item {% if pagination.page == pagination.pages %}disabled{% endif %}">
                        <a class="page-link" href="{{ url_for('admin.logs', page=pagination.next_num, ip=filters.ip, endpoint=filters.endpoint, start_date=filters.start_date, end_date=filters.end_date) }}">下一页</a>
                    </li>
                </ul>
            </nav>
            {% endif %}
        </div>
    </div>
</div>

<!-- 参数查看模态框 -->
<div class="modal fade" id="paramsModal" tabindex="-1" aria-labelledby="paramsModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="paramsModalLabel">请求参数</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <pre id="paramsContent" class="bg-light p-3 rounded" style="max-height: 300px; overflow-y: auto;"></pre>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 查看参数模态框
        var paramsModal = document.getElementById('paramsModal');
        if (paramsModal) {
            paramsModal.addEventListener('show.bs.modal', function(event) {
                var button = event.relatedTarget;
                var params = button.getAttribute('data-params');
                var paramsContent = document.getElementById('paramsContent');
                
                try {
                    // 尝试解析JSON并美化显示
                    var paramsObj = JSON.parse(params);
                    paramsContent.textContent = JSON.stringify(paramsObj, null, 2);
                } catch (e) {
                    // 如果解析失败，直接显示原始内容
                    paramsContent.textContent = params || '无参数';
                }
            });
        }
    });
</script>
{% endblock %} 