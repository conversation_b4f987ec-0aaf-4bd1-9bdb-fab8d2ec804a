from flask import Blueprint, render_template, redirect, url_for, request, flash, jsonify
from flask_login import login_required, current_user
from datetime import datetime, timedelta
import json
import logging

from models import db, Admin, CookieStore, SystemConfig, AccessLog
from auth import admin_required
from cookie_manager import CookieManager

# 设置日志
logger = logging.getLogger('admin')

# 创建Blueprint
admin_bp = Blueprint('admin', __name__, url_prefix='/admin')

@admin_bp.route('/')
@admin_bp.route('/dashboard')
@admin_required
def dashboard():
    """管理员仪表盘"""
    # 获取统计数据
    stats = {
        'cookie_count': CookieStore.query.count(),
        'active_cookie_count': CookieStore.query.filter_by(is_active=True).count(),
        'today_requests': AccessLog.query.filter(
            AccessLog.request_time > datetime.now() - timedelta(days=1)
        ).count(),
        'total_requests': AccessLog.query.count()
    }
    
    # 获取最近的访问记录
    recent_logs = AccessLog.query.order_by(AccessLog.request_time.desc()).limit(10).all()
    
    # 获取请求统计（按接口分组）
    endpoint_stats = db.session.query(
        AccessLog.endpoint, 
        db.func.count(AccessLog.id).label('count')
    ).group_by(AccessLog.endpoint).all()
    
    endpoint_stats = [{'name': stat[0], 'count': stat[1]} for stat in endpoint_stats]
    
    # 获取今日请求统计（按小时分组）
    today = datetime.now().date()
    hourly_stats = db.session.query(
        db.func.strftime('%H', AccessLog.request_time).label('hour'),
        db.func.count(AccessLog.id).label('count')
    ).filter(
        db.func.date(AccessLog.request_time) == today
    ).group_by('hour').all()
    
    hourly_data = [0] * 24
    for stat in hourly_stats:
        try:
            hour = int(stat[0])
            hourly_data[hour] = stat[1]
        except (ValueError, IndexError):
            pass
    
    return render_template(
        'admin/dashboard.html',
        stats=stats,
        recent_logs=recent_logs,
        endpoint_stats=endpoint_stats,
        hourly_data=hourly_data
    )

@admin_bp.route('/cookies')
@admin_required
def cookies():
    """Cookie管理页面"""
    cookies = CookieStore.query.all()
    return render_template('admin/cookies.html', cookies=cookies)

@admin_bp.route('/cookies/add', methods=['POST'])
@admin_required
def add_cookie():
    """添加Cookie"""
    name = request.form.get('name')
    cookie_text = request.form.get('cookie_text')
    vip_type = request.form.get('vip_type', 'vip')
    
    if not name or not cookie_text:
        flash('名称和Cookie内容不能为空', 'danger')
        return redirect(url_for('admin.cookies'))
    
    # 使用CookieManager添加Cookie
    cookie_manager = CookieManager(db=db)
    success = cookie_manager.add_cookie(name, cookie_text, vip_type)
    
    if success:
        flash('Cookie添加成功', 'success')
    else:
        flash('Cookie添加失败，请检查格式是否正确', 'danger')
    
    return redirect(url_for('admin.cookies'))

@admin_bp.route('/cookies/toggle/<int:cookie_id>', methods=['POST'])
@admin_required
def toggle_cookie(cookie_id):
    """启用/禁用Cookie"""
    cookie = CookieStore.query.get_or_404(cookie_id)
    cookie.is_active = not cookie.is_active
    db.session.commit()
    
    status = '启用' if cookie.is_active else '禁用'
    flash(f'Cookie "{cookie.name}" 已{status}', 'success')
    return redirect(url_for('admin.cookies'))

@admin_bp.route('/cookies/delete/<int:cookie_id>', methods=['POST'])
@admin_required
def delete_cookie(cookie_id):
    """删除Cookie"""
    cookie = CookieStore.query.get_or_404(cookie_id)
    db.session.delete(cookie)
    db.session.commit()
    
    flash(f'Cookie "{cookie.name}" 已删除', 'success')
    return redirect(url_for('admin.cookies'))

@admin_bp.route('/settings')
@admin_required
def settings():
    """系统设置页面"""
    configs = SystemConfig.query.all()
    
    # 将配置按类型分组
    rate_limits = []
    qualities = []
    others = []
    
    for config in configs:
        if config.key.startswith('rate_limit'):
            rate_limits.append(config)
        elif 'quality' in config.key:
            qualities.append(config)
        else:
            others.append(config)
    
    return render_template(
        'admin/settings.html',
        rate_limits=rate_limits,
        qualities=qualities,
        others=others
    )

@admin_bp.route('/settings/update', methods=['POST'])
@admin_required
def update_settings():
    """更新系统设置"""
    for key, value in request.form.items():
        if key.startswith('config_'):
            config_key = key[7:]  # 去掉'config_'前缀
            config = SystemConfig.query.filter_by(key=config_key).first()
            if config:
                config.value = value
    
    db.session.commit()
    flash('系统设置已更新', 'success')
    return redirect(url_for('admin.settings'))

@admin_bp.route('/logs')
@admin_required
def logs():
    """访问日志页面"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 50, type=int)
    
    # 过滤条件
    ip = request.args.get('ip')
    endpoint = request.args.get('endpoint')
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    
    query = AccessLog.query
    
    if ip:
        query = query.filter(AccessLog.ip_address.like(f'%{ip}%'))
    if endpoint:
        query = query.filter(AccessLog.endpoint == endpoint)
    if start_date:
        query = query.filter(AccessLog.request_time >= datetime.strptime(start_date, '%Y-%m-%d'))
    if end_date:
        query = query.filter(AccessLog.request_time <= datetime.strptime(end_date, '%Y-%m-%d') + timedelta(days=1))
    
    # 获取所有可用的endpoint
    endpoints = db.session.query(AccessLog.endpoint).distinct().all()
    endpoints = [e[0] for e in endpoints]
    
    # 分页
    pagination = query.order_by(AccessLog.request_time.desc()).paginate(page=page, per_page=per_page)
    logs = pagination.items
    
    return render_template(
        'admin/logs.html',
        logs=logs,
        pagination=pagination,
        endpoints=endpoints,
        filters={
            'ip': ip,
            'endpoint': endpoint,
            'start_date': start_date,
            'end_date': end_date
        }
    )

@admin_bp.route('/profile')
@admin_required
def profile():
    """管理员个人资料页面"""
    return render_template('admin/profile.html')

@admin_bp.route('/api/stats')
@admin_required
def api_stats():
    """获取统计数据API，用于仪表盘图表"""
    days = request.args.get('days', 7, type=int)
    
    # 获取过去n天的每日请求量
    daily_stats = []
    for i in range(days-1, -1, -1):
        date = datetime.now().date() - timedelta(days=i)
        count = AccessLog.query.filter(
            db.func.date(AccessLog.request_time) == date
        ).count()
        daily_stats.append({
            'date': date.strftime('%Y-%m-%d'),
            'count': count
        })
    
    # 获取接口请求分布
    endpoint_stats = db.session.query(
        AccessLog.endpoint, 
        db.func.count(AccessLog.id).label('count')
    ).group_by(AccessLog.endpoint).all()
    
    endpoint_data = [{'name': stat[0], 'value': stat[1]} for stat in endpoint_stats]
    
    return jsonify({
        'daily': daily_stats,
        'endpoints': endpoint_data
    }) 