{% extends "admin/base.html" %}

{% block title %}Cookie管理{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">Cookie管理</h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#addCookieModal">
                <i class="fas fa-plus"></i> 添加Cookie
            </button>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <i class="fas fa-cookie me-1"></i>
            Cookie列表
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>名称</th>
                            <th>VIP类型</th>
                            <th>状态</th>
                            <th>使用次数</th>
                            <th>最后使用时间</th>
                            <th>添加时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for cookie in cookies %}
                        <tr>
                            <td>{{ cookie.id }}</td>
                            <td>{{ cookie.name }}</td>
                            <td>
                                {% if cookie.vip_type == 'svip' %}
                                <span class="badge bg-danger">SVIP</span>
                                {% else %}
                                <span class="badge bg-primary">VIP</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if cookie.is_active %}
                                <span class="badge bg-success">启用</span>
                                {% else %}
                                <span class="badge bg-secondary">禁用</span>
                                {% endif %}
                            </td>
                            <td>{{ cookie.use_count }}</td>
                            <td>{{ cookie.last_used|default('从未使用', true) }}</td>
                            <td>{{ cookie.created_at }}</td>
                            <td>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#viewCookieModal" data-cookie-id="{{ cookie.id }}" data-cookie-text="{{ cookie.cookie_text }}">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <form method="post" action="{{ url_for('admin.toggle_cookie', cookie_id=cookie.id) }}" class="d-inline">
                                        <button type="submit" class="btn btn-sm btn-outline-{{ 'warning' if cookie.is_active else 'success' }}">
                                            <i class="fas fa-{{ 'pause' if cookie.is_active else 'play' }}"></i>
                                        </button>
                                    </form>
                                    <form method="post" action="{{ url_for('admin.delete_cookie', cookie_id=cookie.id) }}" class="d-inline" onsubmit="return confirm('确定要删除这个Cookie吗？');">
                                        <button type="submit" class="btn btn-sm btn-outline-danger">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="8" class="text-center">暂无Cookie记录</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- 添加Cookie模态框 -->
<div class="modal fade" id="addCookieModal" tabindex="-1" aria-labelledby="addCookieModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addCookieModalLabel">添加Cookie</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="{{ url_for('admin.add_cookie') }}">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="name" class="form-label">名称</label>
                        <input type="text" class="form-control" id="name" name="name" required>
                        <div class="form-text">为Cookie添加一个易于识别的名称</div>
                    </div>
                    <div class="mb-3">
                        <label for="cookie_text" class="form-label">Cookie内容</label>
                        <textarea class="form-control" id="cookie_text" name="cookie_text" rows="5" required></textarea>
                        <div class="form-text">格式示例: MUSIC_U=xxx;os=pc;appver=8.9.70;</div>
                    </div>
                    <div class="mb-3">
                        <label for="vip_type" class="form-label">VIP类型</label>
                        <select class="form-select" id="vip_type" name="vip_type">
                            <option value="vip">VIP（普通黑胶会员）</option>
                            <option value="svip">SVIP（黑胶会员SVIP）</option>
                        </select>
                        <div class="form-text">SVIP可以使用全部音质，VIP仅限部分音质</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">添加</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 查看Cookie模态框 -->
<div class="modal fade" id="viewCookieModal" tabindex="-1" aria-labelledby="viewCookieModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="viewCookieModalLabel">查看Cookie</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="view_cookie_text" class="form-label">Cookie内容</label>
                    <textarea class="form-control" id="view_cookie_text" rows="5" readonly></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" id="copy-cookie-btn">复制</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 查看Cookie模态框
        var viewCookieModal = document.getElementById('viewCookieModal');
        if (viewCookieModal) {
            viewCookieModal.addEventListener('show.bs.modal', function(event) {
                var button = event.relatedTarget;
                var cookieText = button.getAttribute('data-cookie-text');
                var cookieTextArea = document.getElementById('view_cookie_text');
                cookieTextArea.value = cookieText;
            });
        }
        
        // 复制Cookie按钮
        var copyButton = document.getElementById('copy-cookie-btn');
        if (copyButton) {
            copyButton.addEventListener('click', function() {
                var cookieTextArea = document.getElementById('view_cookie_text');
                cookieTextArea.select();
                document.execCommand('copy');
                alert('Cookie已复制到剪贴板');
            });
        }
    });
</script>
{% endblock %} 