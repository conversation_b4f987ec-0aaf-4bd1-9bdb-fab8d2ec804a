<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Music Player</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/aplayer/1.10.1/APlayer.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            color: #ffffff;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
            animation: fadeInDown 1s ease;
        }

        .header h1 {
            font-size: 3rem;
            font-weight: 300;
            margin-bottom: 0.5rem;
            background: linear-gradient(45deg, #ffffff, #cccccc);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header p {
            color: #888;
            font-size: 1.1rem;
        }

        .main-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 2.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            animation: fadeInUp 1s ease;
        }

        .mode-selector {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .mode-btn {
            background: transparent;
            border: 2px solid #333;
            color: #888;
            padding: 0.8rem 1.5rem;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .mode-btn:hover,
        .mode-btn.active {
            background: #ffffff;
            color: #1a1a1a;
            border-color: #ffffff;
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(255, 255, 255, 0.2);
        }

        .input-group {
            margin-bottom: 1.5rem;
        }

        .input-label {
            display: block;
            margin-bottom: 0.5rem;
            color: #ccc;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .input-field {
            width: 100%;
            background: rgba(255, 255, 255, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 1rem 1.2rem;
            color: #fff;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .input-field:focus {
            outline: none;
            border-color: #ffffff;
            background: rgba(255, 255, 255, 0.12);
            box-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
        }

        .input-field::placeholder {
            color: #666;
        }

        .select-field {
            width: 100%;
            background: rgba(255, 255, 255, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 1rem 1.2rem;
            color: #fff;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .select-field:focus {
            outline: none;
            border-color: #ffffff;
            background: rgba(255, 255, 255, 0.12);
        }

        .select-field option {
            background: #2d2d2d;
            color: #fff;
        }

        .action-btn {
            background: linear-gradient(45deg, #ffffff, #f0f0f0);
            color: #1a1a1a;
            border: none;
            padding: 1rem 2.5rem;
            border-radius: 50px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            margin: 0 auto;
            min-width: 200px;
        }

        .action-btn:hover {
            background: linear-gradient(45deg, #f0f0f0, #e0e0e0);
            transform: translateY(-3px);
            box-shadow: 0 15px 30px rgba(255, 255, 255, 0.3);
        }

        .action-btn:active {
            transform: translateY(-1px);
        }

        .section {
            display: none;
            animation: fadeIn 0.5s ease;
        }

        .section.active {
            display: block;
        }

        .search-results {
            margin-top: 2rem;
            display: none;
        }

        .results-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-top: 1.5rem;
        }

        .song-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 1.5rem;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .song-card:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .song-info {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .song-cover {
            width: 60px;
            height: 60px;
            border-radius: 10px;
            object-fit: cover;
        }

        .song-details h4 {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 0.3rem;
            color: #fff;
        }

        .song-details p {
            color: #888;
            font-size: 0.9rem;
        }

        .play-btn {
            background: linear-gradient(45deg, #ffffff, #f0f0f0);
            color: #1a1a1a;
            border: none;
            padding: 0.7rem 1.5rem;
            border-radius: 25px;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-left: auto;
        }

        .play-btn:hover {
            background: linear-gradient(45deg, #f0f0f0, #e0e0e0);
            transform: scale(1.05);
        }

        .player-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 2rem;
            margin-top: 2rem;
            display: none;
        }

        .player-info {
            display: flex;
            align-items: center;
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .player-cover {
            width: 120px;
            height: 120px;
            border-radius: 15px;
            object-fit: cover;
        }

        .player-details h3 {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .player-meta {
            color: #888;
            margin-bottom: 1rem;
        }

        .quality-badge {
            background: rgba(255, 255, 255, 0.2);
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.8rem;
            display: inline-block;
            margin-right: 0.5rem;
        }

        .download-btn {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 0.8rem 2rem;
            border-radius: 25px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
            margin-top: 1rem;
        }

        .download-btn:hover {
            background: linear-gradient(45deg, #45a049, #4CAF50);
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(76, 175, 80, 0.3);
            color: white;
            text-decoration: none;
        }

        .audio-player {
            margin-top: 2rem;
            margin-bottom: 2rem;
        }

        .lyrics-section {
            background: rgba(255, 255, 255, 0.03);
            border-radius: 15px;
            padding: 1.5rem;
            max-height: 300px;
            overflow-y: auto;
        }

        .lyrics-title {
            color: #ccc;
            font-size: 0.9rem;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .lyrics-content {
            line-height: 1.8;
            color: #ddd;
        }

        .loading {
            text-align: center;
            padding: 2rem;
            color: #666;
        }

        .spinner {
            width: 16px;
            height: 16px;
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-top: 2px solid #fff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            display: inline-block;
            margin-right: 0.5rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }

            .header h1 {
                font-size: 2rem;
            }

            .main-card {
                padding: 1.5rem;
            }

            .mode-selector {
                flex-direction: column;
                align-items: center;
            }

            .player-info {
                flex-direction: column;
                text-align: center;
            }

            .results-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Music Player</h1>
            <p>发现和播放你喜欢的音乐</p>
        </div>

        <div class="main-card">
            <div class="mode-selector">
                <button class="mode-btn active" data-mode="search">
                    <i class="fas fa-search"></i>
                    搜索音乐
                </button>
                <button class="mode-btn" data-mode="single">
                    <i class="fas fa-music"></i>
                    单曲播放
                </button>
                <button class="mode-btn" data-mode="playlist">
                    <i class="fas fa-list"></i>
                    歌单播放
                </button>
                <button class="mode-btn" data-mode="album">
                    <i class="fas fa-compact-disc"></i>
                    专辑播放
                </button>
            </div>

            <div id="search-section" class="section active">
                <div class="input-group">
                    <label class="input-label">搜索关键词</label>
                    <input type="text" id="search-keywords" class="input-field" placeholder="输入歌曲、歌手或专辑名称">
                </div>
                <div class="input-group">
                    <label class="input-label">搜索数量</label>
                    <select id="search-limit" class="select-field">
                        <option value="10">10首歌曲</option>
                        <option value="20">20首歌曲</option>
                        <option value="30">30首歌曲</option>
                        <option value="50">50首歌曲</option>
                    </select>
                </div>
                <button id="search-btn" class="action-btn">
                    <i class="fas fa-search"></i>
                    开始搜索
                </button>
            </div>

            <div id="single-section" class="section">
                <div class="input-group">
                    <label class="input-label">歌曲ID或链接</label>
                    <input type="text" id="song-id" class="input-field" placeholder="输入网易云音乐歌曲ID或链接">
                </div>
                <div class="input-group">
                    <label class="input-label">音质选择</label>
                    <select id="quality" class="select-field">
                        <option value="standard">标准音质</option>
                        <option value="exhigh">极高音质</option>
                        <option value="lossless">无损音质</option>
                        <option value="hires">Hi-Res音质</option>
                        <option value="sky">沉浸环绕声</option>
                        <option value="jyeffect">高清环绕声</option>
                        <option value="jymaster">超清母带</option>
                    </select>
                </div>
                <button id="single-btn" class="action-btn">
                    <i class="fas fa-play"></i>
                    获取播放链接
                </button>
            </div>

            <div id="playlist-section" class="section">
                <div class="input-group">
                    <label class="input-label">歌单ID或链接</label>
                    <input type="text" id="playlist-id" class="input-field" placeholder="输入网易云音乐歌单ID或链接">
                </div>
                <button id="playlist-btn" class="action-btn">
                    <i class="fas fa-list"></i>
                    获取歌单
                </button>
            </div>

            <div id="album-section" class="section">
                <div class="input-group">
                    <label class="input-label">专辑ID或链接</label>
                    <input type="text" id="album-id" class="input-field" placeholder="输入网易云音乐专辑ID或链接">
                </div>
                <button id="album-btn" class="action-btn">
                    <i class="fas fa-compact-disc"></i>
                    获取专辑
                </button>
            </div>
        </div>

        <div id="search-results" class="search-results">
            <h3><i class="fas fa-music"></i> 搜索结果</h3>
            <div id="results-grid" class="results-grid"></div>
        </div>

        <div id="player-card" class="player-card">
            <div class="player-info">
                <img id="player-cover" class="player-cover" src="" alt="专辑封面">
                <div class="player-details">
                    <h3 id="player-title"></h3>
                    <div class="player-meta">
                        <div id="player-artist"></div>
                        <div id="player-album"></div>
                    </div>
                    <div>
                        <span id="player-quality" class="quality-badge"></span>
                        <span id="player-size" class="quality-badge"></span>
                    </div>
                    <a id="download-link" class="download-btn" href="" target="_blank">
                        <i class="fas fa-download"></i>
                        下载音乐
                    </a>
                </div>
            </div>
            <div class="audio-player">
                <div id="aplayer"></div>
            </div>
            <div class="lyrics-section">
                <div class="lyrics-title">
                    <i class="fas fa-quote-left"></i>
                    歌词
                </div>
                <div id="lyrics-content" class="lyrics-content"></div>
            </div>
        </div>

        <div id="playlist-results" class="player-card">
            <div class="player-info">
                <img id="playlist-cover" class="player-cover" src="" alt="歌单封面">
                <div class="player-details">
                    <h3 id="playlist-title"></h3>
                    <div class="player-meta">
                        <div id="playlist-creator"></div>
                        <div id="playlist-description"></div>
                    </div>
                    <div>
                        <span id="playlist-count" class="quality-badge"></span>
                    </div>
                </div>
            </div>
            <div id="playlist-tracks" class="results-grid"></div>
        </div>

        <div id="album-results" class="player-card">
            <div class="player-info">
                <img id="album-cover" class="player-cover" src="" alt="专辑封面">
                <div class="player-details">
                    <h3 id="album-title"></h3>
                    <div class="player-meta">
                        <div id="album-artist"></div>
                        <div id="album-description"></div>
                    </div>
                    <div>
                        <span id="album-count" class="quality-badge"></span>
                    </div>
                </div>
            </div>
            <div id="album-tracks" class="results-grid"></div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/aplayer/1.10.1/APlayer.min.js"></script>
    <script>
        // 音质选项映射
        const qualityOptions = {
            'standard': '标准音质',
            'exhigh': '极高音质', 
            'lossless': '无损音质',
            'hires': 'Hi-Res音质',
            'sky': '沉浸环绕声',
            'jyeffect': '高清环绕声',
            'jymaster': '超清母带'
        };
        
        // 加载系统配置
        function loadSystemConfig() {
            $.get('/api/config', function(config) {
                // 更新音质选择器
                const qualitySelect = $('#quality');
                const currentValue = qualitySelect.val();
                qualitySelect.empty();
                
                config.allowed_qualities.forEach(function(quality) {
                    if (qualityOptions[quality]) {
                        const option = $('<option></option>')
                            .attr('value', quality)
                            .text(qualityOptions[quality]);
                        
                        // 尝试保持原选择，如果不在允许列表中则使用默认
                        if (quality === currentValue || (currentValue && !config.allowed_qualities.includes(currentValue) && quality === config.default_quality)) {
                            option.attr('selected', true);
                        } else if (!currentValue && quality === config.default_quality) {
                            option.attr('selected', true);
                        }
                        
                        qualitySelect.append(option);
                    }
                });
                
                // 添加提示信息
                const qualityGroup = qualitySelect.closest('.input-group');
                let helpText = qualityGroup.find('.config-help');
                if (helpText.length === 0) {
                    helpText = $('<div class="config-help" style="color: #888; font-size: 0.85rem; margin-top: 0.5rem;"></div>');
                    qualityGroup.append(helpText);
                }
                
                const allowedQualityNames = config.allowed_qualities.map(q => qualityOptions[q]).join('、');
                helpText.text(`系统允许的音质：${allowedQualityNames}`);
                
            }).fail(function() {
                console.warn('无法加载系统配置，使用默认设置');
            });
        }
        
        $(document).ready(function() {
            // 加载系统配置
            loadSystemConfig();
            
            // 模式切换
            $('.mode-btn').click(function() {
                $('.mode-btn').removeClass('active');
                $(this).addClass('active');
                
                $('.section').removeClass('active');
                const mode = $(this).data('mode');
                $(`#${mode}-section`).addClass('active');
                
                // 隐藏结果区域
                $('#search-results, #player-card, #playlist-results, #album-results').hide();
            });

            // 歌词处理函数
            function lrctrim(lyrics) {
                const lines = lyrics.split('\n');
                const data = [];
                lines.forEach((line, index) => {
                    const matches = line.match(/\[(\d{2}):(\d{2}[\.:]?\d*)]/);
                    if (matches) {
                        const minutes = parseInt(matches[1], 10);
                        const seconds = parseFloat(matches[2].replace('.', ':')) || 0;
                        const timestamp = minutes * 60000 + seconds * 1000;
                        let text = line.replace(/\[\d{2}:\d{2}[\.:]?\d*\]/g, '').trim();
                        text = text.replace(/\s\s+/g, ' ');
                        data.push([timestamp, index, text]);
                    }
                });
                data.sort((a, b) => a[0] - b[0]);
                return data;
            }

            function lrctran(originalLyrics, translatedLyrics) {
                const lines = originalLyrics.split('\n');
                const translatedLines = translatedLyrics.split('\n');
                const translatedMap = {};
                
                translatedLines.forEach(line => {
                    const matches = line.match(/\[(\d{2}:\d{2}[\.:]?\d*)\](.*)/);
                    if (matches) {
                        const time = matches[1];
                        const text = matches[2].trim();
                        if (text) {
                            translatedMap[time] = text;
                        }
                    }
                });
                
                const result = lines.map(line => {
                    const matches = line.match(/\[(\d{2}:\d{2}[\.:]?\d*)\](.*)/);
                    if (matches) {
                        const time = matches[1];
                        const originalText = matches[2].trim();
                        const translatedText = translatedMap[time];
                        
                        if (originalText && translatedText) {
                            return originalText + '\n' + translatedText;
                        } else if (originalText) {
                            return originalText;
                        }
                    }
                    return line;
                });
                
                return result.join('\n');
            }

            // 搜索功能
            $('#search-btn').click(function() {
                const keywords = $('#search-keywords').val().trim();
                const limit = $('#search-limit').val();
                
                if (!keywords) {
                    alert('请输入搜索关键词');
                    return;
                }

                const btn = $(this);
                btn.html('<div class="spinner"></div>搜索中...');
                
                $.ajax({
                    url: '/Search',
                    method: 'GET',
                    data: { keywords: keywords, limit: limit },
                    dataType: 'json',
                    success: function(data) {
                        if (data.status === 200 && data.result.length > 0) {
                            const grid = $('#results-grid');
                            grid.empty();
                            
                            data.result.forEach(function(song) {
                                const card = `
                                    <div class="song-card">
                                        <div class="song-info">
                                            <img src="${song.picUrl}" class="song-cover" alt="专辑封面">
                                            <div class="song-details">
                                                <h4>${song.name}</h4>
                                                <p>${song.artists} · ${song.album}</p>
                                            </div>
                                        </div>
                                        <button class="play-btn" data-id="${song.id}">
                                            <i class="fas fa-play"></i>
                                            播放
                                        </button>
                                    </div>
                                `;
                                grid.append(card);
                            });
                            
                            $('#search-results').show();
                        } else {
                            $('#results-grid').html('<div class="loading">未找到相关音乐</div>');
                            $('#search-results').show();
                        }
                    },
                    error: function() {
                        $('#results-grid').html('<div class="loading">搜索失败，请重试</div>');
                        $('#search-results').show();
                    },
                    complete: function() {
                        btn.html('<i class="fas fa-search"></i>开始搜索');
                    }
                });
            });

            // 搜索结果播放按钮
            $(document).on('click', '.play-btn', function() {
                const songId = $(this).data('id');
                $('#song-id').val(songId);
                $('.mode-btn[data-mode="single"]').click();
            });

            // 单曲播放
            $('#single-btn').click(function() {
                const songId = $('#song-id').val().trim();
                const quality = $('#quality').val();
                
                if (!songId) {
                    alert('请输入歌曲ID或链接');
                    return;
                }

                const btn = $(this);
                btn.html('<div class="spinner"></div>获取中...');
                
                $.post('/Song_V1', { 
                    url: songId, 
                    level: quality, 
                    type: 'json' 
                }, function(data) {
                    if (data.status === 200) {
                        $('#player-cover').attr('src', data.pic);
                        $('#player-title').text(data.name);
                        $('#player-artist').text('歌手：' + data.ar_name);
                        $('#player-album').text('专辑：' + data.al_name);
                        $('#player-quality').text(data.level);
                        $('#player-size').text(data.size);
                        $('#download-link').attr('href', data.url);
                        
                        // 检查音质是否被系统限制
                        const requestedQuality = qualityOptions[quality];
                        const actualQuality = data.level;
                        if (requestedQuality && !actualQuality.includes(requestedQuality.replace('音质', '').replace('Hi-Res', 'Hires'))) {
                            // 显示音质降级提示
                            setTimeout(function() {
                                alert(`注意：您请求的${requestedQuality}不可用，系统已为您提供${actualQuality}。这可能是由于系统设置限制或Cookie权限不足。`);
                            }, 500);
                        }
                        
                        let processedLyrics = data.lyric;
                        if (data.tlyric) {
                            processedLyrics = lrctran(data.lyric, data.tlyric);
                        }
                        $('#lyrics-content').html(processedLyrics.replace(/\n/g, '<br>'));
                        
                        // 初始化APlayer
                        $('#aplayer').empty();
                        new APlayer({
                            container: document.getElementById('aplayer'),
                            lrcType: 1,
                            audio: [{
                                name: data.name,
                                artist: data.ar_name,
                                url: data.url,
                                cover: data.pic,
                                lrc: processedLyrics
                            }]
                        });
                        
                        $('#player-card').show();
                        $('#search-results, #playlist-results, #album-results').hide();
                    } else {
                        alert('获取失败：' + (data.msg || '未知错误'));
                    }
                }, 'json').fail(function() {
                    alert('获取失败，请重试');
                }).always(function() {
                    btn.html('<i class="fas fa-play"></i>获取播放链接');
                });
            });

            // 歌单播放
            $('#playlist-btn').click(function() {
                let playlistId = $('#playlist-id').val().trim();
                
                if (!playlistId) {
                    alert('请输入歌单ID或链接');
                    return;
                }

                // 支持链接提取ID
                const idMatch = playlistId.match(/playlist\?id=(\d+)/);
                if (idMatch) playlistId = idMatch[1];

                const btn = $(this);
                btn.html('<div class="spinner"></div>获取中...');
                
                $.get('/Playlist', { id: playlistId }, function(data) {
                    if (data.status === 200) {
                        const pl = data.playlist;
                        $('#playlist-cover').attr('src', pl.coverImgUrl);
                        $('#playlist-title').text(pl.name);
                        $('#playlist-creator').text('创建者：' + pl.creator);
                        $('#playlist-description').text(pl.description || '');
                        $('#playlist-count').text(`${pl.trackCount} 首歌曲`);
                        
                        const tracksGrid = $('#playlist-tracks');
                        tracksGrid.empty();
                        
                        pl.tracks.forEach(function(song, index) {
                            const card = `
                                <div class="song-card">
                                    <div class="song-info">
                                        <img src="${song.picUrl}" class="song-cover" alt="专辑封面">
                                        <div class="song-details">
                                            <h4>${index + 1}. ${song.name}</h4>
                                            <p>${song.artists} · ${song.album}</p>
                                        </div>
                                    </div>
                                    <button class="play-btn" data-id="${song.id}">
                                        <i class="fas fa-play"></i>
                                        播放
                                    </button>
                                </div>
                            `;
                            tracksGrid.append(card);
                        });
                        
                        $('#playlist-results').show();
                        $('#search-results, #player-card, #album-results').hide();
                    } else {
                        alert('获取歌单失败：' + (data.msg || '未知错误'));
                    }
                }, 'json').fail(function() {
                    alert('获取歌单失败，请重试');
                }).always(function() {
                    btn.html('<i class="fas fa-list"></i>获取歌单');
                });
            });

            // 专辑播放
            $('#album-btn').click(function() {
                let albumId = $('#album-id').val().trim();
                
                if (!albumId) {
                    alert('请输入专辑ID或链接');
                    return;
                }

                // 支持链接提取ID
                const idMatch = albumId.match(/album\?id=(\d+)/);
                if (idMatch) albumId = idMatch[1];

                const btn = $(this);
                btn.html('<div class="spinner"></div>获取中...');
                
                $.get('/Album', { id: albumId }, function(data) {
                    if (data.status === 200) {
                        const al = data.album;
                        $('#album-cover').attr('src', al.coverImgUrl);
                        $('#album-title').text(al.name);
                        $('#album-artist').text('艺术家：' + al.artist);
                        $('#album-description').text(al.description || '');
                        $('#album-count').text(`${al.songs.length} 首歌曲`);
                        
                        const tracksGrid = $('#album-tracks');
                        tracksGrid.empty();
                        
                        al.songs.forEach(function(song, index) {
                            const card = `
                                <div class="song-card">
                                    <div class="song-info">
                                        <img src="${song.picUrl}" class="song-cover" alt="专辑封面">
                                        <div class="song-details">
                                            <h4>${index + 1}. ${song.name}</h4>
                                            <p>${song.artists} · ${song.album}</p>
                                        </div>
                                    </div>
                                    <button class="play-btn" data-id="${song.id}">
                                        <i class="fas fa-play"></i>
                                        播放
                                    </button>
                                </div>
                            `;
                            tracksGrid.append(card);
                        });
                        
                        $('#album-results').show();
                        $('#search-results, #player-card, #playlist-results').hide();
                    } else {
                        alert('获取专辑失败：' + (data.msg || '未知错误'));
                    }
                }, 'json').fail(function() {
                    alert('获取专辑失败，请重试');
                }).always(function() {
                    btn.html('<i class="fas fa-compact-disc"></i>获取专辑');
                });
            });

            // 回车键搜索
            $('#search-keywords').keypress(function(e) {
                if (e.which === 13) {
                    $('#search-btn').click();
                }
            });
        });
    </script>
</body>
</html>
