from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime
import json
from flask_login import UserMixin

db = SQLAlchemy()

class Admin(db.Model, UserMixin):
    """管理员表"""
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(64), unique=True, nullable=False)
    password_hash = db.Column(db.String(128), nullable=False)
    is_active = db.Column(db.<PERSON><PERSON><PERSON>, default=True)
    created_at = db.Column(db.DateTime, default=datetime.now)
    last_login = db.Column(db.DateTime)

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

    def to_dict(self):
        return {
            'id': self.id,
            'username': self.username,
            'is_active': self.is_active,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            'last_login': self.last_login.strftime('%Y-%m-%d %H:%M:%S') if self.last_login else None
        }

    # 直接实现Flask-Login所需的方法
    def get_id(self):
        return str(self.id)
    
    def is_authenticated(self):
        return True
    
    def is_anonymous(self):
        return False

class AccessLog(db.Model):
    """访问记录表"""
    id = db.Column(db.Integer, primary_key=True)
    ip_address = db.Column(db.String(64), nullable=False)
    endpoint = db.Column(db.String(64), nullable=False)
    method = db.Column(db.String(10), nullable=False)
    status_code = db.Column(db.Integer)
    request_time = db.Column(db.DateTime, default=datetime.now)
    user_agent = db.Column(db.String(256))
    params = db.Column(db.Text)  # 存储请求参数，JSON格式

    def to_dict(self):
        return {
            'id': self.id,
            'ip_address': self.ip_address,
            'endpoint': self.endpoint,
            'method': self.method,
            'status_code': self.status_code,
            'request_time': self.request_time.strftime('%Y-%m-%d %H:%M:%S'),
            'user_agent': self.user_agent,
            'params': json.loads(self.params) if self.params else {}
        }

class CookieStore(db.Model):
    """Cookie存储表"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(64), nullable=False)  # Cookie名称/描述
    cookie_text = db.Column(db.Text, nullable=False)  # Cookie内容
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.now)
    last_used = db.Column(db.DateTime)
    use_count = db.Column(db.Integer, default=0)
    vip_type = db.Column(db.String(10), default='vip')  # vip或svip，表示Cookie等级

    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'cookie_text': self.cookie_text,
            'is_active': self.is_active,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            'last_used': self.last_used.strftime('%Y-%m-%d %H:%M:%S') if self.last_used else None,
            'use_count': self.use_count,
            'vip_type': self.vip_type
        }

class SystemConfig(db.Model):
    """系统配置表"""
    id = db.Column(db.Integer, primary_key=True)
    key = db.Column(db.String(64), unique=True, nullable=False)
    value = db.Column(db.Text, nullable=False)
    description = db.Column(db.String(256))
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    def to_dict(self):
        return {
            'id': self.id,
            'key': self.key,
            'value': self.value,
            'description': self.description,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S')
        }

# 默认系统配置
DEFAULT_CONFIGS = [
    {
        'key': 'rate_limit_global',
        'value': '60/minute',
        'description': '全局API访问频率限制'
    },
    {
        'key': 'rate_limit_song',
        'value': '30/minute',
        'description': '单曲解析接口访问频率限制'
    },
    {
        'key': 'rate_limit_search',
        'value': '20/minute',
        'description': '搜索接口访问频率限制'
    },
    {
        'key': 'rate_limit_playlist',
        'value': '10/minute',
        'description': '歌单解析接口访问频率限制'
    },
    {
        'key': 'rate_limit_album',
        'value': '10/minute',
        'description': '专辑解析接口访问频率限制'
    },
    {
        'key': 'default_quality',
        'value': 'lossless',
        'description': '默认音质'
    },
    {
        'key': 'allowed_qualities',
        'value': 'standard,exhigh,lossless,hires',
        'description': '允许的音质列表，逗号分隔'
    },
    {
        'key': 'cookie_rotation',
        'value': 'true',
        'description': '是否启用Cookie轮换'
    }
] 