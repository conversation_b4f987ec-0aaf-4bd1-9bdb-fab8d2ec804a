from flask import Blueprint, render_template, redirect, url_for, request, flash, session
from flask_login import LoginManager, login_user, logout_user, login_required, current_user
from werkzeug.security import check_password_hash
from functools import wraps
from datetime import datetime
import logging

from models import Admin, db

# 设置日志
logger = logging.getLogger('auth')

# 创建Blueprint
auth_bp = Blueprint('auth', __name__)

# 初始化LoginManager
login_manager = LoginManager()

@login_manager.user_loader
def load_user(user_id):
    """加载用户"""
    return Admin.query.get(int(user_id))

# 设置登录视图
login_manager.login_view = 'auth.login'
login_manager.login_message = '请先登录'
login_manager.login_message_category = 'warning'

def admin_required(f):
    """管理员权限装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            flash('请先登录', 'warning')
            return redirect(url_for('auth.login', next=request.url))
        return f(*args, **kwargs)
    return decorated_function

@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    """管理员登录"""
    if current_user.is_authenticated:
        return redirect(url_for('admin.dashboard'))
    
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        remember = request.form.get('remember', False) == 'on'
        
        user = Admin.query.filter_by(username=username).first()
        
        if user and user.check_password(password):
            if not user.is_active:
                flash('该账号已被禁用，请联系管理员', 'danger')
                return render_template('admin/login.html')
            
            login_user(user, remember=remember)
            user.last_login = datetime.now()
            db.session.commit()
            
            logger.info(f"管理员 {username} 登录成功")
            
            next_page = request.args.get('next')
            if next_page:
                return redirect(next_page)
            return redirect(url_for('admin.dashboard'))
        else:
            flash('用户名或密码错误', 'danger')
            logger.warning(f"管理员登录失败: {username}")
    
    return render_template('admin/login.html')

@auth_bp.route('/logout')
@login_required
def logout():
    """管理员注销"""
    username = current_user.username
    logout_user()
    logger.info(f"管理员 {username} 已注销")
    flash('已成功注销', 'success')
    return redirect(url_for('auth.login'))

@auth_bp.route('/change_password', methods=['POST'])
@login_required
def change_password():
    """修改密码"""
    old_password = request.form.get('old_password')
    new_password = request.form.get('new_password')
    confirm_password = request.form.get('confirm_password')
    
    if not current_user.check_password(old_password):
        flash('当前密码错误', 'danger')
        return redirect(url_for('admin.profile'))
    
    if new_password != confirm_password:
        flash('两次输入的新密码不一致', 'danger')
        return redirect(url_for('admin.profile'))
    
    current_user.set_password(new_password)
    db.session.commit()
    
    logger.info(f"管理员 {current_user.username} 修改了密码")
    flash('密码修改成功', 'success')
    return redirect(url_for('admin.profile')) 